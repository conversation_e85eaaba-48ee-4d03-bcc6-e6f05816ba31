import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { authService } from '../services/authService';

const Login: React.FC = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('test123');
  const [isLoading, setIsLoading] = useState(false);

  // Limpar qualquer autenticação anterior ao carregar a página de login
  useEffect(() => {
    // Não verificar autenticação aqui para evitar loops
    // A página de login deve sempre ser acessível
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !password) {
      toast.error('Por favor, preencha todos os campos');
      return;
    }

    setIsLoading(true);
    
    try {
      await authService.login({ email, password });
      toast.success('Login realizado com sucesso!');

      // Redirecionamento usando navigate
      navigate('/dashboard', { replace: true });
    } catch (error: any) {
      console.error('Erro no login:', error);
      const message = error.response?.data?.message || 'Erro ao fazer login';
      toast.error(message);
    } finally {
      setIsLoading(false);
    }
  };

  // Removido: handleDemoLogin e handleForceLogout para simplificar

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-blue-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-8">
        <div className="bg-white rounded-2xl shadow-xl p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="mx-auto h-20 w-20 mb-4 flex items-center justify-center">
              <img
                src="/LogoRafa.png"
                alt="Rafaela Cuida"
                className="h-full w-full object-contain"
                onError={(e) => {
                  // Fallback para emoji se a imagem não carregar
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling!.style.display = 'flex';
                }}
              />
              <div className="hidden h-20 w-20 bg-gradient-to-r from-pink-500 to-blue-500 rounded-full items-center justify-center">
                <span className="text-white text-3xl">👶</span>
              </div>
            </div>
            <h2 className="text-3xl font-bold text-gray-900">
              Rafaela Cuida
            </h2>
            <p className="text-gray-600 mt-2">
              Sistema de Acompanhamento Gestacional
            </p>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Email
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors"
                placeholder="<EMAIL>"
                disabled={isLoading}
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                Senha
              </label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors"
                placeholder="••••••••"
                disabled={isLoading}
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-pink-500 to-blue-500 text-white py-3 px-4 rounded-lg font-medium hover:from-pink-600 hover:to-blue-600 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Entrando...
                </div>
              ) : (
                'Entrar'
              )}
            </button>
          </form>

          {/* Removido: Seções de demonstração, credenciais e features para simplificar */}
        </div>

        {/* Footer */}
        <div className="text-center text-sm text-gray-500">
          <p>Sistema desenvolvido com ❤️ para o cuidado materno</p>
        </div>
      </div>
    </div>
  );
};

export default Login;
