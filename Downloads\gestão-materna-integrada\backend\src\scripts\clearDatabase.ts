import { supabase } from '../config/supabase';

/**
 * <PERSON>ript para limpar todos os contatos e mensagens do banco de dados
 * ATENÇÃO: Esta operação é irreversível!
 */

async function clearDatabase() {
  console.log('🗑️ Iniciando limpeza completa do banco de dados...');
  console.log('⚠️ ATENÇÃO: Esta operação irá remover TODOS os dados!');
  
  try {
    // 1. <PERSON>par todas as mensagens primeiro (devido às foreign keys)
    console.log('📧 Removendo todas as mensagens...');
    const { error: messagesError } = await supabase
      .from('messages')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Deleta todos os registros

    if (messagesError) {
      console.error('❌ Erro ao remover mensagens:', messagesError);
      throw messagesError;
    }

    console.log('✅ Todas as mensagens foram removidas');

    // 2. Limpar todos os contatos
    console.log('👥 Removendo todos os contatos...');
    const { error: contactsError } = await supabase
      .from('contacts')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Deleta todos os registros

    if (contactsError) {
      console.error('❌ Erro ao remover contatos:', contactsError);
      throw contactsError;
    }

    console.log('✅ Todos os contatos foram removidos');

    // 3. Verificar se a limpeza foi bem-sucedida
    console.log('🔍 Verificando limpeza...');
    
    const { count: messageCount } = await supabase
      .from('messages')
      .select('*', { count: 'exact', head: true });

    const { count: contactCount } = await supabase
      .from('contacts')
      .select('*', { count: 'exact', head: true });

    console.log(`📊 Contatos restantes: ${contactCount || 0}`);
    console.log(`📊 Mensagens restantes: ${messageCount || 0}`);

    if ((contactCount || 0) === 0 && (messageCount || 0) === 0) {
      console.log('🎉 Limpeza completa realizada com sucesso!');
      console.log('✨ Banco de dados zerado e pronto para novas interações');
    } else {
      console.warn('⚠️ Alguns registros podem não ter sido removidos');
    }

  } catch (error) {
    console.error('❌ Erro durante a limpeza do banco:', error);
    throw error;
  }
}

// Executar o script
if (require.main === module) {
  clearDatabase()
    .then(() => {
      console.log('🏁 Script de limpeza finalizado');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Falha na limpeza:', error);
      process.exit(1);
    });
}

export { clearDatabase };
