2025/06/15-10:51:53.682 1e80 Reusing MANIFEST C:\Users\<USER>\Downloads\gestão-materna-integrada\backend\tokens\rafaela-audio\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/MANIFEST-000001
2025/06/15-10:51:53.682 1e80 Recovering log #782
2025/06/15-10:51:53.683 1e80 Reusing old log C:\Users\<USER>\Downloads\gestão-materna-integrada\backend\tokens\rafaela-audio\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/000782.log 
2025/06/15-10:51:53.684 1e80 Delete type=2 #781
2025/06/15-10:51:53.693 a8b0 Level-0 table #787: started
2025/06/15-10:51:53.695 a8b0 Level-0 table #787: 14766 bytes OK
2025/06/15-10:51:53.697 a8b0 Delete type=0 #782
2025/06/15-10:51:53.698 a8b0 Manual compaction at level-0 from ' \x0f\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x10\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:53.698 a8b0 Manual compaction at level-1 from ' \x0f\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x10\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x0f\x01\x00\x00\xc8\x0a\x00j\x00o\x00b\x00s\x00-\x00s\x00t\x00o\x00r\x00e' @ 1494560 : 0
2025/06/15-10:51:53.698 a8b0 Compacting 1@1 + 1@2 files
2025/06/15-10:51:53.772 a8b0 Generated table #788@1: 17241 keys, 708158 bytes
2025/06/15-10:51:53.772 a8b0 Compacted 1@1 + 1@2 files => 708158 bytes
2025/06/15-10:51:53.773 a8b0 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/15-10:51:53.773 a8b0 Delete type=2 #787
2025/06/15-10:51:53.773 a8b0 Manual compaction at level-1 from ' \x0f\x01\x00\x00\xc8\x0a\x00j\x00o\x00b\x00s\x00-\x00s\x00t\x00o\x00r\x00e' @ 1494560 : 0 .. ' \x10\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:53.774 a8b0 Level-0 table #790: started
2025/06/15-10:51:53.777 a8b0 Level-0 table #790: 6034 bytes OK
2025/06/15-10:51:53.779 a8b0 Delete type=2 #784
2025/06/15-10:51:53.779 a8b0 Delete type=0 #786
2025/06/15-10:51:53.781 a8b0 Manual compaction at level-0 from ' \x15\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x16\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:53.781 e4ec Manual compaction at level-1 from ' \x15\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x16\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x16\x01\x00\x00\xc8\x11\x00w\x00o\x00r\x00k\x00e\x00r\x00_\x00w\x00a\x00m\x00_\x00e\x00v\x00e\x00n\x00t\x00s' @ 1494933 : 1
2025/06/15-10:51:53.781 e4ec Compacting 1@1 + 1@2 files
2025/06/15-10:51:53.815 e4ec Generated table #791@1: 17537 keys, 712212 bytes
2025/06/15-10:51:53.815 e4ec Compacted 1@1 + 1@2 files => 712212 bytes
2025/06/15-10:51:53.815 e4ec compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/15-10:51:53.816 e4ec Delete type=2 #790
2025/06/15-10:51:53.816 e4ec Manual compaction at level-1 from ' \x16\x01\x00\x00\xc8\x11\x00w\x00o\x00r\x00k\x00e\x00r\x00_\x00w\x00a\x00m\x00_\x00e\x00v\x00e\x00n\x00t\x00s' @ 1494933 : 1 .. ' \x16\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:53.849 fcec Level-0 table #793: started
2025/06/15-10:51:53.857 fcec Level-0 table #793: 112897 bytes OK
2025/06/15-10:51:53.858 fcec Delete type=2 #788
2025/06/15-10:51:53.858 fcec Delete type=0 #789
2025/06/15-10:51:53.862 fcec Manual compaction at level-0 from ' \x0b\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x0c\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:53.862 fcec Manual compaction at level-1 from ' \x0b\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x0c\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x0b\x01J \x04\x02\x01.\x00f\x00a\x00l\x00s\x00e\x00_\x005\x005\x008\x004\x008\x008\x005\x000\x001\x005\x008\x002\x00@\x00c\x00.\x00u\x00s\x00_\x003\x00E\x00B\x000\x00F\x001\x00E\x001\x001\x00F\x00C\x003\x007\x003\x007\x004\x007\x00B\x007\x004\x002\x00F\x03\x00\x00\x80\xf3\xa4\x13\xdaA\x00\x03\x00\x00\x00\x00\x00\x00\x1c@' @ 1502837 : 0
2025/06/15-10:51:53.862 fcec Compacting 1@1 + 1@2 files
2025/06/15-10:51:53.885 fcec Generated table #794@1: 9672 keys, 359361 bytes
2025/06/15-10:51:53.885 fcec Compacted 1@1 + 1@2 files => 359361 bytes
2025/06/15-10:51:53.885 fcec compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/15-10:51:53.885 fcec Delete type=2 #793
2025/06/15-10:51:53.887 dbcc Manual compaction at level-1 from ' \x0b\x01J \x04\x02\x01.\x00f\x00a\x00l\x00s\x00e\x00_\x005\x005\x008\x004\x008\x008\x005\x000\x001\x005\x008\x002\x00@\x00c\x00.\x00u\x00s\x00_\x003\x00E\x00B\x000\x00F\x001\x00E\x001\x001\x00F\x00C\x003\x007\x003\x007\x004\x007\x00B\x007\x004\x002\x00F\x03\x00\x00\x80\xf3\xa4\x13\xdaA\x00\x03\x00\x00\x00\x00\x00\x00\x1c@' @ 1502837 : 0 .. ' \x0c\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:53.889 80a0 Level-0 table #796: started
2025/06/15-10:51:53.890 80a0 Level-0 table #796: 615 bytes OK
2025/06/15-10:51:53.891 80a0 Delete type=2 #791
2025/06/15-10:51:53.891 80a0 Delete type=0 #792
2025/06/15-10:51:53.892 80a0 Manual compaction at level-0 from ' \x0e\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x0f\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:53.892 80a0 Manual compaction at level-1 from ' \x0e\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x0f\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x0e\x01\x01\x02\x03\x00\x00\x00\x00\x00\x00\xf0?' @ 1502873 : 0
2025/06/15-10:51:53.892 80a0 Compacting 1@1 + 1@2 files
2025/06/15-10:51:53.909 80a0 Generated table #797@1: 9636 keys, 358610 bytes
2025/06/15-10:51:53.909 80a0 Compacted 1@1 + 1@2 files => 358610 bytes
2025/06/15-10:51:53.909 80a0 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/15-10:51:53.910 80a0 Delete type=2 #796
2025/06/15-10:51:53.910 80a0 Manual compaction at level-1 from ' \x0e\x01\x01\x02\x03\x00\x00\x00\x00\x00\x00\xf0?' @ 1502873 : 0 .. ' \x0f\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:53.911 80a0 Level-0 table #799: started
2025/06/15-10:51:53.912 80a0 Level-0 table #799: 394 bytes OK
2025/06/15-10:51:53.913 80a0 Delete type=2 #794
2025/06/15-10:51:53.913 80a0 Delete type=0 #795
2025/06/15-10:51:53.914 80a0 Manual compaction at level-0 from ' \x14\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x15\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:53.914 80a0 Manual compaction at level-1 from ' \x14\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x15\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x14\x01\x00\x00\xc8\x0a\x00q\x00p\x00l\x00-\x00e\x00v\x00e\x00n\x00t\x00s' @ 1502890 : 0
2025/06/15-10:51:53.914 80a0 Compacting 1@1 + 1@2 files
2025/06/15-10:51:53.928 80a0 Generated table #800@1: 9620 keys, 358368 bytes
2025/06/15-10:51:53.928 80a0 Compacted 1@1 + 1@2 files => 358368 bytes
2025/06/15-10:51:53.928 80a0 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/15-10:51:53.929 80a0 Delete type=2 #799
2025/06/15-10:51:53.929 80a0 Manual compaction at level-1 from ' \x14\x01\x00\x00\xc8\x0a\x00q\x00p\x00l\x00-\x00e\x00v\x00e\x00n\x00t\x00s' @ 1502890 : 0 .. ' \x15\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:53.930 e4ec Level-0 table #802: started
2025/06/15-10:51:53.931 e4ec Level-0 table #802: 410 bytes OK
2025/06/15-10:51:53.932 e4ec Delete type=2 #797
2025/06/15-10:51:53.932 e4ec Delete type=0 #798
2025/06/15-10:51:53.933 e4ec Manual compaction at level-0 from ' \x0c\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x0d\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:53.933 e4ec Manual compaction at level-1 from ' \x0c\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x0d\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x0c\x01\x00\x00\xc8\x06\x00s\x00t\x00a\x00t\x00u\x00s' @ 1502910 : 0
2025/06/15-10:51:53.933 e4ec Compacting 1@1 + 1@2 files
2025/06/15-10:51:53.945 e4ec Generated table #803@1: 9600 keys, 358086 bytes
2025/06/15-10:51:53.945 e4ec Compacted 1@1 + 1@2 files => 358086 bytes
2025/06/15-10:51:53.946 e4ec compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/15-10:51:53.946 e4ec Delete type=2 #802
2025/06/15-10:51:53.947 e4ec Manual compaction at level-1 from ' \x0c\x01\x00\x00\xc8\x06\x00s\x00t\x00a\x00t\x00u\x00s' @ 1502910 : 0 .. ' \x0d\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:53.974 10cd4 Level-0 table #805: started
2025/06/15-10:51:53.978 10cd4 Level-0 table #805: 123593 bytes OK
2025/06/15-10:51:53.979 10cd4 Delete type=2 #800
2025/06/15-10:51:53.979 10cd4 Delete type=0 #801
2025/06/15-10:51:53.980 10cd4 Manual compaction at level-0 from '\x00\xfd\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\xfe\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:53.981 10cd4 Manual compaction at level-1 from '\x00\xfd\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\xfe\x00\x00\x00' @ 0 : 0; will stop at '\x00\xfd\x06 \x03\x00\x00\x00\x00\x00\x80V@\x00\x01\x0b\x00I\x00d\x00T\x00t\x00l\x009\x000\x00D\x00a\x00y\x00s' @ 1512172 : 0
2025/06/15-10:51:53.981 10cd4 Compacting 1@1 + 1@2 files
2025/06/15-10:51:53.990 10cd4 Generated table #806@1: 338 keys, 4835 bytes
2025/06/15-10:51:53.990 10cd4 Compacted 1@1 + 1@2 files => 4835 bytes
2025/06/15-10:51:53.990 10cd4 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/15-10:51:53.991 10cd4 Delete type=2 #805
2025/06/15-10:51:53.991 10cd4 Manual compaction at level-1 from '\x00\xfd\x06 \x03\x00\x00\x00\x00\x00\x80V@\x00\x01\x0b\x00I\x00d\x00T\x00t\x00l\x009\x000\x00D\x00a\x00y\x00s' @ 1512172 : 0 .. '\x00\xfe\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:53.992 80a0 Level-0 table #808: started
2025/06/15-10:51:53.994 80a0 Level-0 table #808: 697 bytes OK
2025/06/15-10:51:53.995 80a0 Delete type=2 #803
2025/06/15-10:51:53.995 80a0 Delete type=0 #804
2025/06/15-10:51:53.996 80a0 Manual compaction at level-0 from ' \x0d\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x0e\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:53.996 80a0 Manual compaction at level-1 from ' \x0d\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x0e\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x16\x01\x04\x01\x01\x0b\x00W\x00A\x00U\x00n\x00k\x00n\x00o\x00w\x00n\x00I\x00D' @ 1512174 : 1
2025/06/15-10:51:53.996 80a0 Compacting 1@1 + 1@2 files
2025/06/15-10:51:53.997 80a0 Generated table #809@1: 317 keys, 4595 bytes
2025/06/15-10:51:53.997 80a0 Compacted 1@1 + 1@2 files => 4595 bytes
2025/06/15-10:51:53.997 80a0 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/15-10:51:53.998 80a0 Delete type=2 #808
2025/06/15-10:51:53.998 80a0 Manual compaction at level-1 from ' \x16\x01\x04\x01\x01\x0b\x00W\x00A\x00U\x00n\x00k\x00n\x00o\x00w\x00n\x00I\x00D' @ 1512174 : 1 .. ' \x0e\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:54.231 80a0 Manual compaction at level-0 from ' \x17\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x18\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:54.232 dbcc Level-0 table #811: started
2025/06/15-10:51:54.234 dbcc Level-0 table #811: 2902 bytes OK
2025/06/15-10:51:54.234 dbcc Delete type=2 #806
2025/06/15-10:51:54.234 dbcc Delete type=0 #807
2025/06/15-10:51:54.235 dbcc Manual compaction at level-0 from ' \x18\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x19\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:54.236 dbcc Level-0 table #813: started
2025/06/15-10:51:54.237 dbcc Level-0 table #813: 857 bytes OK
2025/06/15-10:51:54.237 dbcc Delete type=0 #810
2025/06/15-10:51:54.238 dbcc Manual compaction at level-0 from ' \x19\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x1a\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x19\x01\x00\x00\x05' @ 1512482 : 0
2025/06/15-10:51:54.238 dbcc Compacting 1@0 + 1@1 files
2025/06/15-10:51:54.239 dbcc Generated table #814@0: 227 keys, 2730 bytes
2025/06/15-10:51:54.239 dbcc Compacted 1@0 + 1@1 files => 2730 bytes
2025/06/15-10:51:54.239 dbcc compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/15-10:51:54.240 dbcc Delete type=2 #813
2025/06/15-10:51:54.240 dbcc Manual compaction at level-0 from ' \x19\x01\x00\x00\x05' @ 1512482 : 0 .. ' \x1a\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:54.241 dbcc Level-0 table #816: started
2025/06/15-10:51:54.242 dbcc Level-0 table #816: 747 bytes OK
2025/06/15-10:51:54.244 dbcc Delete type=2 #811
2025/06/15-10:51:54.244 dbcc Delete type=0 #812
2025/06/15-10:51:54.245 dbcc Manual compaction at level-0 from ' \x1a\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x1b\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x1a\x01\x00\x00\x05' @ 1512516 : 0
2025/06/15-10:51:54.245 dbcc Compacting 1@0 + 1@1 files
2025/06/15-10:51:54.246 dbcc Generated table #817@0: 237 keys, 2950 bytes
2025/06/15-10:51:54.246 dbcc Compacted 1@0 + 1@1 files => 2950 bytes
2025/06/15-10:51:54.246 dbcc compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/15-10:51:54.246 dbcc Delete type=2 #816
2025/06/15-10:51:54.247 dbcc Manual compaction at level-0 from ' \x1a\x01\x00\x00\x05' @ 1512516 : 0 .. ' \x1b\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:54.247 e4ec Level-0 table #819: started
2025/06/15-10:51:54.249 e4ec Level-0 table #819: 3905 bytes OK
2025/06/15-10:51:54.249 e4ec Delete type=2 #814
2025/06/15-10:51:54.249 e4ec Delete type=0 #815
2025/06/15-10:51:54.250 e4ec Manual compaction at level-0 from ' \x1b\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x1c\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x1b\x01\x00\x00\x05' @ 1512626 : 0
2025/06/15-10:51:54.250 e4ec Compacting 1@0 + 1@1 files
2025/06/15-10:51:54.252 e4ec Generated table #820@0: 322 keys, 6255 bytes
2025/06/15-10:51:54.252 e4ec Compacted 1@0 + 1@1 files => 6255 bytes
2025/06/15-10:51:54.252 e4ec compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/15-10:51:54.252 e4ec Delete type=2 #819
2025/06/15-10:51:54.253 e4ec Manual compaction at level-0 from ' \x1b\x01\x00\x00\x05' @ 1512626 : 0 .. ' \x1c\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:54.254 a8b0 Level-0 table #822: started
2025/06/15-10:51:54.255 a8b0 Level-0 table #822: 389 bytes OK
2025/06/15-10:51:54.256 a8b0 Delete type=2 #817
2025/06/15-10:51:54.256 a8b0 Delete type=0 #818
2025/06/15-10:51:54.257 a8b0 Manual compaction at level-0 from ' \x1c\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x1d\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x1c\x01\x00\x00\x05' @ 1512639 : 0
2025/06/15-10:51:54.257 a8b0 Compacting 1@0 + 1@1 files
2025/06/15-10:51:54.258 a8b0 Generated table #823@0: 326 keys, 6284 bytes
2025/06/15-10:51:54.258 a8b0 Compacted 1@0 + 1@1 files => 6284 bytes
2025/06/15-10:51:54.258 a8b0 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/15-10:51:54.258 a8b0 Delete type=2 #822
2025/06/15-10:51:54.259 a8b0 Manual compaction at level-0 from ' \x1c\x01\x00\x00\x05' @ 1512639 : 0 .. ' \x1d\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:54.260 a8b0 Level-0 table #825: started
2025/06/15-10:51:54.261 a8b0 Level-0 table #825: 996 bytes OK
2025/06/15-10:51:54.261 a8b0 Delete type=2 #820
2025/06/15-10:51:54.261 a8b0 Delete type=0 #821
2025/06/15-10:51:54.262 a8b0 Manual compaction at level-0 from ' \x1d\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x1e\x01\x00\x00\x00' @ 0 : 0; will stop at ' "\x01\x00\x00\xc8\x04\x00k\x00e\x00y\x00s' @ 1512656 : 1
2025/06/15-10:51:54.262 a8b0 Compacting 1@0 + 1@1 files
2025/06/15-10:51:54.264 a8b0 Generated table #826@0: 367 keys, 6821 bytes
2025/06/15-10:51:54.264 a8b0 Compacted 1@0 + 1@1 files => 6821 bytes
2025/06/15-10:51:54.264 a8b0 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/15-10:51:54.264 a8b0 Manual compaction at level-0 from ' "\x01\x00\x00\xc8\x04\x00k\x00e\x00y\x00s' @ 1512656 : 1 .. ' \x1e\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:54.265 7e54 Level-0 table #828: started
2025/06/15-10:51:54.267 7e54 Level-0 table #828: 879 bytes OK
2025/06/15-10:51:54.268 7e54 Delete type=2 #823
2025/06/15-10:51:54.268 7e54 Delete type=0 #824
2025/06/15-10:51:54.268 7e54 Delete type=2 #825
2025/06/15-10:51:54.269 7e54 Manual compaction at level-0 from ' !\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' "\x01\x00\x00\x00' @ 0 : 0; will stop at ' "\x01\x02\x02\x03\x00\x00\x00\x00\x00\x00\xf0?' @ 1512709 : 1
2025/06/15-10:51:54.269 7e54 Compacting 1@0 + 1@1 files
2025/06/15-10:51:54.271 7e54 Generated table #829@0: 379 keys, 7362 bytes
2025/06/15-10:51:54.271 7e54 Compacted 1@0 + 1@1 files => 7362 bytes
2025/06/15-10:51:54.272 7e54 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/15-10:51:54.272 7e54 Delete type=2 #828
2025/06/15-10:51:54.272 7e54 Manual compaction at level-0 from ' "\x01\x02\x02\x03\x00\x00\x00\x00\x00\x00\xf0?' @ 1512709 : 1 .. ' "\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:54.274 a8b0 Level-0 table #831: started
2025/06/15-10:51:54.275 a8b0 Level-0 table #831: 417 bytes OK
2025/06/15-10:51:54.275 a8b0 Delete type=2 #826
2025/06/15-10:51:54.275 a8b0 Delete type=0 #827
2025/06/15-10:51:54.276 a8b0 Manual compaction at level-0 from ' \x1f\x01\x00\x00\x00' @ 72057594037927935 : 1 .. '  \x01\x00\x00\x00' @ 0 : 0; will stop at ' #\x01\x00\x00\x05' @ 1512725 : 1
2025/06/15-10:51:54.276 a8b0 Compacting 1@0 + 1@1 files
2025/06/15-10:51:54.277 a8b0 Generated table #832@0: 379 keys, 7285 bytes
2025/06/15-10:51:54.277 a8b0 Compacted 1@0 + 1@1 files => 7285 bytes
2025/06/15-10:51:54.277 a8b0 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/15-10:51:54.277 a8b0 Delete type=2 #831
2025/06/15-10:51:54.278 a8b0 Manual compaction at level-0 from ' #\x01\x00\x00\x05' @ 1512725 : 1 .. '  \x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:54.279 a8b0 Level-0 table #834: started
2025/06/15-10:51:54.280 a8b0 Level-0 table #834: 251 bytes OK
2025/06/15-10:51:54.280 a8b0 Delete type=2 #829
2025/06/15-10:51:54.280 a8b0 Delete type=0 #830
2025/06/15-10:51:54.281 a8b0 Manual compaction at level-0 from '  \x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' !\x01\x00\x00\x00' @ 0 : 0; will stop at '  \x01\x00\x00\x05' @ 1512735 : 0
2025/06/15-10:51:54.281 a8b0 Compacting 1@0 + 1@1 files
2025/06/15-10:51:54.282 a8b0 Generated table #835@0: 377 keys, 7223 bytes
2025/06/15-10:51:54.282 a8b0 Compacted 1@0 + 1@1 files => 7223 bytes
2025/06/15-10:51:54.283 a8b0 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/15-10:51:54.283 a8b0 Delete type=2 #834
2025/06/15-10:51:54.283 a8b0 Manual compaction at level-0 from '  \x01\x00\x00\x05' @ 1512735 : 0 .. ' !\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:54.284 a8b0 Level-0 table #837: started
2025/06/15-10:51:54.285 a8b0 Level-0 table #837: 252 bytes OK
2025/06/15-10:51:54.286 a8b0 Delete type=2 #832
2025/06/15-10:51:54.286 a8b0 Delete type=0 #833
2025/06/15-10:51:54.286 a8b0 Manual compaction at level-0 from ' \x1e\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x1f\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x1e\x01\x00\x00\x05' @ 1512741 : 0
2025/06/15-10:51:54.286 a8b0 Compacting 1@0 + 1@1 files
2025/06/15-10:51:54.288 a8b0 Generated table #838@0: 375 keys, 7120 bytes
2025/06/15-10:51:54.288 a8b0 Compacted 1@0 + 1@1 files => 7120 bytes
2025/06/15-10:51:54.289 a8b0 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/15-10:51:54.289 a8b0 Delete type=2 #837
2025/06/15-10:51:54.289 a8b0 Manual compaction at level-0 from ' \x1e\x01\x00\x00\x05' @ 1512741 : 0 .. ' \x1f\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:54.305 7e54 Manual compaction at level-0 from ' $\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' %\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:56.489 10cd4 Level-0 table #840: started
2025/06/15-10:51:56.491 10cd4 Level-0 table #840: 4804 bytes OK
2025/06/15-10:51:56.492 10cd4 Delete type=2 #835
2025/06/15-10:51:56.492 10cd4 Delete type=0 #836
2025/06/15-10:51:56.494 10cd4 Manual compaction at level-0 from ' #\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' $\x01\x00\x00\x00' @ 0 : 0; will stop at ' %\x01\x00\x00\x05' @ 1513093 : 1
2025/06/15-10:51:56.494 10cd4 Compacting 1@0 + 1@1 files
2025/06/15-10:51:56.497 10cd4 Generated table #841@0: 465 keys, 6707 bytes
2025/06/15-10:51:56.497 10cd4 Compacted 1@0 + 1@1 files => 6707 bytes
2025/06/15-10:51:56.497 10cd4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/15-10:51:56.497 10cd4 Delete type=2 #840
2025/06/15-10:51:56.498 10cd4 Manual compaction at level-0 from ' %\x01\x00\x00\x05' @ 1513093 : 1 .. ' $\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:56.500 fcec Level-0 table #843: started
2025/06/15-10:51:56.501 fcec Level-0 table #843: 3330 bytes OK
2025/06/15-10:51:56.502 fcec Delete type=2 #838
2025/06/15-10:51:56.502 fcec Delete type=0 #839
2025/06/15-10:51:56.504 fcec Manual compaction at level-0 from ' \x16\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x17\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x16\x01\x06 \x03\x00\x00\x00\x00\x00\x80V@\x00\x01\x0b\x00I\x00d\x00T\x00t\x00l\x009\x000\x00D\x00a\x00y\x00s' @ 1513378 : 0
2025/06/15-10:51:56.504 fcec Compacting 1@0 + 1@1 files
2025/06/15-10:51:56.508 fcec Generated table #844@0: 575 keys, 6020 bytes
2025/06/15-10:51:56.508 fcec Compacted 1@0 + 1@1 files => 6020 bytes
2025/06/15-10:51:56.509 fcec compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/15-10:51:56.509 fcec Delete type=2 #843
2025/06/15-10:51:56.510 fcec Manual compaction at level-0 from ' \x16\x01\x06 \x03\x00\x00\x00\x00\x00\x80V@\x00\x01\x0b\x00I\x00d\x00T\x00t\x00l\x009\x000\x00D\x00a\x00y\x00s' @ 1513378 : 0 .. ' \x17\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:56.510 fcec Manual compaction at level-1 from ' \x16\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x17\x01\x00\x00\x00' @ 0 : 0; will stop at ' %\x01\x00\x00\x05' @ 1513093 : 1
2025/06/15-10:51:56.510 fcec Compacting 1@1 + 1@2 files
2025/06/15-10:51:56.512 fcec Generated table #845@1: 41 keys, 1188 bytes
2025/06/15-10:51:56.512 fcec Compacted 1@1 + 1@2 files => 1188 bytes
2025/06/15-10:51:56.512 fcec compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/15-10:51:56.513 fcec Delete type=2 #844
2025/06/15-10:51:56.513 fcec Manual compaction at level-1 from ' %\x01\x00\x00\x05' @ 1513093 : 1 .. ' \x17\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:56.515 7e54 Level-0 table #847: started
2025/06/15-10:51:56.517 7e54 Level-0 table #847: 509 bytes OK
2025/06/15-10:51:56.518 7e54 Delete type=2 #809
2025/06/15-10:51:56.518 7e54 Delete type=2 #841
2025/06/15-10:51:56.518 7e54 Delete type=0 #842
2025/06/15-10:51:56.521 7e54 Manual compaction at level-0 from ' "\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' #\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:56.521 7e54 Manual compaction at level-1 from ' "\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' #\x01\x00\x00\x00' @ 0 : 0; will stop at ' "\x01\x02\x02\x03\x00\x00\x00\x00\x00\x00\xf0?' @ 1513407 : 0
2025/06/15-10:51:56.521 7e54 Compacting 1@1 + 1@2 files
2025/06/15-10:51:56.523 7e54 Generated table #848@1: 12 keys, 478 bytes
2025/06/15-10:51:56.523 7e54 Compacted 1@1 + 1@2 files => 478 bytes
2025/06/15-10:51:56.523 7e54 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/15-10:51:56.523 7e54 Delete type=2 #847
2025/06/15-10:51:56.524 7e54 Manual compaction at level-1 from ' "\x01\x02\x02\x03\x00\x00\x00\x00\x00\x00\xf0?' @ 1513407 : 0 .. ' #\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:56.947 10820 Level-0 table #850: started
2025/06/15-10:51:56.949 10820 Level-0 table #850: 6653 bytes OK
2025/06/15-10:51:56.950 10820 Delete type=2 #845
2025/06/15-10:51:56.950 10820 Delete type=0 #846
2025/06/15-10:51:56.952 10820 Manual compaction at level-0 from ' %\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' &\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:56.952 10820 Manual compaction at level-1 from ' %\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' &\x01\x00\x00\x00' @ 0 : 0; will stop at ' 0\x01\x00\x00\x05' @ 1513944 : 1
2025/06/15-10:51:56.952 10820 Compacting 1@1 + 1@2 files
2025/06/15-10:51:56.954 10820 Generated table #851@1: 184 keys, 2712 bytes
2025/06/15-10:51:56.954 10820 Compacted 1@1 + 1@2 files => 2712 bytes
2025/06/15-10:51:56.954 10820 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/15-10:51:56.955 10820 Delete type=2 #850
2025/06/15-10:51:56.955 10820 Manual compaction at level-1 from ' 0\x01\x00\x00\x05' @ 1513944 : 1 .. ' &\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:56.956 10cd4 Level-0 table #853: started
2025/06/15-10:51:56.957 10cd4 Level-0 table #853: 982 bytes OK
2025/06/15-10:51:56.958 10cd4 Delete type=2 #848
2025/06/15-10:51:56.958 10cd4 Delete type=0 #849
2025/06/15-10:51:56.959 1e80 Manual compaction at level-0 from ' '\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' (\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:56.959 1e80 Manual compaction at level-1 from ' '\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' (\x01\x00\x00\x00' @ 0 : 0; will stop at ' '\x01\x00\x00\x05' @ 1514009 : 0
2025/06/15-10:51:56.959 1e80 Compacting 1@1 + 1@2 files
2025/06/15-10:51:56.961 1e80 Generated table #854@1: 192 keys, 2987 bytes
2025/06/15-10:51:56.961 1e80 Compacted 1@1 + 1@2 files => 2987 bytes
2025/06/15-10:51:56.962 1e80 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/15-10:51:56.962 1e80 Delete type=2 #853
2025/06/15-10:51:56.963 10cd4 Manual compaction at level-1 from ' '\x01\x00\x00\x05' @ 1514009 : 0 .. ' (\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:56.963 1e80 Level-0 table #856: started
2025/06/15-10:51:56.965 1e80 Level-0 table #856: 1426 bytes OK
2025/06/15-10:51:56.966 1e80 Delete type=2 #851
2025/06/15-10:51:56.966 1e80 Delete type=0 #852
2025/06/15-10:51:56.967 10820 Manual compaction at level-0 from ' (\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' )\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:56.967 10820 Manual compaction at level-1 from ' (\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' )\x01\x00\x00\x00' @ 0 : 0; will stop at ' (\x01\x00\x00\x05' @ 1514060 : 0
2025/06/15-10:51:56.967 10820 Compacting 1@1 + 1@2 files
2025/06/15-10:51:56.969 10820 Generated table #857@1: 211 keys, 3710 bytes
2025/06/15-10:51:56.969 10820 Compacted 1@1 + 1@2 files => 3710 bytes
2025/06/15-10:51:56.970 10820 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/15-10:51:56.970 10820 Delete type=2 #856
2025/06/15-10:51:56.971 10820 Manual compaction at level-1 from ' (\x01\x00\x00\x05' @ 1514060 : 0 .. ' )\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:56.972 3020 Level-0 table #859: started
2025/06/15-10:51:56.974 3020 Level-0 table #859: 3212 bytes OK
2025/06/15-10:51:56.974 3020 Delete type=2 #854
2025/06/15-10:51:56.975 3020 Delete type=0 #855
2025/06/15-10:51:56.976 3020 Manual compaction at level-0 from ' )\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' *\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:56.976 3020 Manual compaction at level-1 from ' )\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' *\x01\x00\x00\x00' @ 0 : 0; will stop at ' )\x01\x00\x00\x05' @ 1514153 : 0
2025/06/15-10:51:56.976 3020 Compacting 1@1 + 1@2 files
2025/06/15-10:51:56.979 3020 Generated table #860@1: 277 keys, 6451 bytes
2025/06/15-10:51:56.979 3020 Compacted 1@1 + 1@2 files => 6451 bytes
2025/06/15-10:51:56.979 3020 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/15-10:51:56.980 3020 Delete type=2 #859
2025/06/15-10:51:56.980 3020 Manual compaction at level-1 from ' )\x01\x00\x00\x05' @ 1514153 : 0 .. ' *\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:56.981 10820 Level-0 table #862: started
2025/06/15-10:51:56.982 10820 Level-0 table #862: 989 bytes OK
2025/06/15-10:51:56.983 10820 Delete type=2 #857
2025/06/15-10:51:56.983 10820 Delete type=0 #858
2025/06/15-10:51:56.984 10820 Manual compaction at level-0 from ' *\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' +\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:56.984 10820 Manual compaction at level-1 from ' *\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' +\x01\x00\x00\x00' @ 0 : 0; will stop at ' 1\x01\x00\x00\xc8\x04\x00k\x00e\x00y\x00s' @ 1514170 : 1
2025/06/15-10:51:56.984 10820 Compacting 1@1 + 1@2 files
2025/06/15-10:51:56.987 10820 Generated table #863@1: 315 keys, 6961 bytes
2025/06/15-10:51:56.987 10820 Compacted 1@1 + 1@2 files => 6961 bytes
2025/06/15-10:51:56.987 10820 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/15-10:51:56.987 10820 Delete type=2 #862
2025/06/15-10:51:56.988 10820 Manual compaction at level-1 from ' 1\x01\x00\x00\xc8\x04\x00k\x00e\x00y\x00s' @ 1514170 : 1 .. ' +\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:56.989 10820 Level-0 table #865: started
2025/06/15-10:51:56.990 10820 Level-0 table #865: 882 bytes OK
2025/06/15-10:51:56.991 10820 Delete type=2 #860
2025/06/15-10:51:56.991 10820 Delete type=0 #861
2025/06/15-10:51:56.991 10820 Manual compaction at level-0 from ' +\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' ,\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:56.991 10820 Manual compaction at level-1 from ' +\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' ,\x01\x00\x00\x00' @ 0 : 0; will stop at ' 1\x01\x02\x02\x03\x00\x00\x00\x00\x00\x00\xf0?' @ 1514223 : 1
2025/06/15-10:51:56.991 10820 Compacting 1@1 + 1@2 files
2025/06/15-10:51:56.993 10820 Generated table #866@1: 323 keys, 7376 bytes
2025/06/15-10:51:56.993 10820 Compacted 1@1 + 1@2 files => 7376 bytes
2025/06/15-10:51:56.993 10820 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/15-10:51:56.994 10820 Delete type=2 #865
2025/06/15-10:51:56.994 10820 Manual compaction at level-1 from ' 1\x01\x02\x02\x03\x00\x00\x00\x00\x00\x00\xf0?' @ 1514223 : 1 .. ' ,\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:56.996 10820 Level-0 table #868: started
2025/06/15-10:51:56.997 10820 Level-0 table #868: 417 bytes OK
2025/06/15-10:51:56.997 10820 Delete type=2 #863
2025/06/15-10:51:56.997 10820 Delete type=0 #864
2025/06/15-10:51:56.999 10820 Manual compaction at level-0 from ' ,\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' -\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:56.999 10820 Manual compaction at level-1 from ' ,\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' -\x01\x00\x00\x00' @ 0 : 0; will stop at ' 2\x01\x00\x00\x05' @ 1514237 : 1
2025/06/15-10:51:56.999 10820 Compacting 1@1 + 1@2 files
2025/06/15-10:51:57.002 10820 Generated table #869@1: 320 keys, 7314 bytes
2025/06/15-10:51:57.002 10820 Compacted 1@1 + 1@2 files => 7314 bytes
2025/06/15-10:51:57.002 10820 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/15-10:51:57.002 10820 Delete type=2 #868
2025/06/15-10:51:57.002 10820 Manual compaction at level-1 from ' 2\x01\x00\x00\x05' @ 1514237 : 1 .. ' -\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:57.003 10cd4 Level-0 table #871: started
2025/06/15-10:51:57.004 10cd4 Level-0 table #871: 2577 bytes OK
2025/06/15-10:51:57.005 10cd4 Delete type=2 #866
2025/06/15-10:51:57.005 10cd4 Delete type=0 #867
2025/06/15-10:51:57.006 10cd4 Manual compaction at level-0 from ' 0\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' 1\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:57.006 10cd4 Manual compaction at level-1 from ' 0\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' 1\x01\x00\x00\x00' @ 0 : 0; will stop at ' 2\x01\x00\x00\xc8\x13\x00s\x00i\x00g\x00n\x00e\x00d\x00-\x00p\x00r\x00e\x00k\x00e\x00y\x00-\x00s\x00t\x00o\x00r\x00e' @ 1514336 : 1
2025/06/15-10:51:57.006 10cd4 Compacting 1@1 + 1@2 files
2025/06/15-10:51:57.008 10cd4 Generated table #872@1: 468 keys, 9544 bytes
2025/06/15-10:51:57.008 10cd4 Compacted 1@1 + 1@2 files => 9544 bytes
2025/06/15-10:51:57.009 10cd4 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/15-10:51:57.009 10cd4 Delete type=2 #871
2025/06/15-10:51:57.009 10cd4 Manual compaction at level-1 from ' 2\x01\x00\x00\xc8\x13\x00s\x00i\x00g\x00n\x00e\x00d\x00-\x00p\x00r\x00e\x00k\x00e\x00y\x00-\x00s\x00t\x00o\x00r\x00e' @ 1514336 : 1 .. ' 1\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:57.010 70fc Level-0 table #874: started
2025/06/15-10:51:57.011 70fc Level-0 table #874: 537 bytes OK
2025/06/15-10:51:57.011 70fc Delete type=2 #869
2025/06/15-10:51:57.012 70fc Delete type=0 #870
2025/06/15-10:51:57.012 70fc Manual compaction at level-0 from ' .\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' /\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:57.012 70fc Manual compaction at level-1 from ' .\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' /\x01\x00\x00\x00' @ 0 : 0; will stop at ' 3\x01\x00\x00\x05' @ 1514417 : 1
2025/06/15-10:51:57.012 70fc Compacting 1@1 + 1@2 files
2025/06/15-10:51:57.015 70fc Generated table #875@1: 468 keys, 9464 bytes
2025/06/15-10:51:57.015 70fc Compacted 1@1 + 1@2 files => 9464 bytes
2025/06/15-10:51:57.015 70fc compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/15-10:51:57.016 70fc Delete type=2 #874
2025/06/15-10:51:57.016 70fc Manual compaction at level-1 from ' 3\x01\x00\x00\x05' @ 1514417 : 1 .. ' /\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:57.017 70fc Level-0 table #877: started
2025/06/15-10:51:57.018 70fc Level-0 table #877: 252 bytes OK
2025/06/15-10:51:57.019 70fc Delete type=2 #872
2025/06/15-10:51:57.019 70fc Delete type=0 #873
2025/06/15-10:51:57.019 70fc Manual compaction at level-0 from ' /\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' 0\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:57.019 70fc Manual compaction at level-1 from ' /\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' 0\x01\x00\x00\x00' @ 0 : 0; will stop at ' /\x01\x00\x00\x05' @ 1514437 : 0
2025/06/15-10:51:57.019 70fc Compacting 1@1 + 1@2 files
2025/06/15-10:51:57.023 70fc Generated table #878@1: 462 keys, 9380 bytes
2025/06/15-10:51:57.023 70fc Compacted 1@1 + 1@2 files => 9380 bytes
2025/06/15-10:51:57.023 70fc compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/15-10:51:57.024 70fc Delete type=2 #877
2025/06/15-10:51:57.024 70fc Manual compaction at level-1 from ' /\x01\x00\x00\x05' @ 1514437 : 0 .. ' 0\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:57.025 70fc Level-0 table #880: started
2025/06/15-10:51:57.026 70fc Level-0 table #880: 252 bytes OK
2025/06/15-10:51:57.026 70fc Delete type=2 #875
2025/06/15-10:51:57.026 70fc Delete type=0 #876
2025/06/15-10:51:57.027 70fc Manual compaction at level-0 from ' -\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' .\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:57.027 70fc Manual compaction at level-1 from ' -\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' .\x01\x00\x00\x00' @ 0 : 0; will stop at ' -\x01\x00\x00\x05' @ 1514443 : 0
2025/06/15-10:51:57.027 70fc Compacting 1@1 + 1@2 files
2025/06/15-10:51:57.029 70fc Generated table #881@1: 456 keys, 9208 bytes
2025/06/15-10:51:57.029 70fc Compacted 1@1 + 1@2 files => 9208 bytes
2025/06/15-10:51:57.029 70fc compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/15-10:51:57.030 70fc Delete type=2 #880
2025/06/15-10:51:57.030 70fc Manual compaction at level-1 from ' -\x01\x00\x00\x05' @ 1514443 : 0 .. ' .\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:57.032 10820 Level-0 table #883: started
2025/06/15-10:51:57.033 10820 Level-0 table #883: 1567 bytes OK
2025/06/15-10:51:57.033 10820 Delete type=2 #878
2025/06/15-10:51:57.033 10820 Delete type=0 #879
2025/06/15-10:51:57.034 10820 Manual compaction at level-0 from ' 3\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' 4\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/15-10:51:57.034 10820 Manual compaction at level-1 from ' 3\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' 4\x01\x00\x00\x00' @ 0 : 0; will stop at ' 3\x01\x00\x00\x05' @ 1514609 : 0
2025/06/15-10:51:57.034 10820 Compacting 1@1 + 1@2 files
2025/06/15-10:51:57.036 10820 Generated table #884@1: 290 keys, 6090 bytes
2025/06/15-10:51:57.036 10820 Compacted 1@1 + 1@2 files => 6090 bytes
2025/06/15-10:51:57.036 10820 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/15-10:51:57.037 10820 Delete type=2 #883
2025/06/15-10:51:57.037 10820 Manual compaction at level-1 from ' 3\x01\x00\x00\x05' @ 1514609 : 0 .. ' 4\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
