"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.testAudioEndpoint = testAudioEndpoint;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const form_data_1 = __importDefault(require("form-data"));
const node_fetch_1 = __importDefault(require("node-fetch"));
/**
 * Script para testar a funcionalidade de áudio
 */
async function testAudioEndpoint() {
    console.log('🎵 Testando funcionalidade de áudio...');
    try {
        // 1. Criar um arquivo de áudio de teste (simulado com header WAV válido)
        const wavHeader = Buffer.from([
            0x52, 0x49, 0x46, 0x46, // "RIFF"
            0x24, 0x08, 0x00, 0x00, // File size
            0x57, 0x41, 0x56, 0x45, // "WAVE"
            0x66, 0x6D, 0x74, 0x20, // "fmt "
            0x10, 0x00, 0x00, 0x00, // Subchunk1Size
            0x01, 0x00, // AudioFormat (PCM)
            0x01, 0x00, // NumChannels (mono)
            0x44, 0xAC, 0x00, 0x00, // SampleRate (44100)
            0x88, 0x58, 0x01, 0x00, // ByteRate
            0x02, 0x00, // BlockAlign
            0x10, 0x00, // BitsPerSample
            0x64, 0x61, 0x74, 0x61, // "data"
            0x00, 0x08, 0x00, 0x00 // Subchunk2Size
        ]);
        // Adicionar alguns dados de áudio simulados
        const audioData = Buffer.alloc(2048, 0); // Silêncio
        const testAudioData = Buffer.concat([wavHeader, audioData]);
        const testAudioPath = path_1.default.join(__dirname, 'test-audio.wav');
        // Escrever arquivo temporário
        fs_1.default.writeFileSync(testAudioPath, testAudioData);
        console.log('📁 Arquivo de teste criado:', testAudioPath);
        // 2. Primeiro, criar um contato de teste
        console.log('👤 Criando contato de teste...');
        const contactResponse = await (0, node_fetch_1.default)('http://localhost:3334/api/contacts', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: 'Teste Áudio',
                phone: `84${Date.now().toString().slice(-8)}`, // Telefone único baseado no timestamp
                babyGender: 'unknown'
            })
        });
        if (!contactResponse.ok) {
            throw new Error(`Erro ao criar contato: ${contactResponse.statusText}`);
        }
        const contact = await contactResponse.json();
        console.log('✅ Contato criado:', contact.id);
        // 3. Testar endpoint de áudio
        console.log('🎵 Testando endpoint de áudio...');
        const formData = new form_data_1.default();
        formData.append('audio', fs_1.default.createReadStream(testAudioPath), {
            filename: 'test-audio.wav',
            contentType: 'audio/wav'
        });
        formData.append('contactId', contact.id);
        formData.append('message', 'Mensagem de teste com áudio');
        const audioResponse = await (0, node_fetch_1.default)('http://localhost:3334/api/chat/audio', {
            method: 'POST',
            body: formData
        });
        console.log('📡 Status da resposta:', audioResponse.status);
        if (audioResponse.ok) {
            const result = await audioResponse.json();
            console.log('✅ Resposta do endpoint de áudio:', result);
        }
        else {
            const error = await audioResponse.text();
            console.error('❌ Erro na resposta:', error);
        }
        // 4. Limpar arquivo temporário
        fs_1.default.unlinkSync(testAudioPath);
        console.log('🗑️ Arquivo temporário removido');
    }
    catch (error) {
        console.error('❌ Erro no teste de áudio:', error);
    }
}
// Executar teste
if (require.main === module) {
    testAudioEndpoint()
        .then(() => {
        console.log('🏁 Teste de áudio finalizado');
        process.exit(0);
    })
        .catch((error) => {
        console.error('💥 Falha no teste:', error);
        process.exit(1);
    });
}
