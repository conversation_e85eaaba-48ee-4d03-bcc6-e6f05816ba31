"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.wppConnectIntegration = exports.WppConnectIntegration = void 0;
const wppconnect = __importStar(require("@wppconnect-team/wppconnect"));
// Audio handler removido (sistema simplificado)
const gemini_1 = require("./gemini");
const contactService_1 = require("./contactService");
const messageService_1 = require("./messageService");
/**
 * Integração completa com wppConnect para processamento automático de áudios
 * Gerencia conexão, autenticação e processamento de mensagens
 */
class WppConnectIntegration {
    constructor(geminiService) {
        this.client = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = null;
        this.isInitializing = false;
        this.keepAliveInterval = null; // Para manter a sessão ativa
        this.geminiService = geminiService;
        this.connectionStatus = {
            isConnected: false,
            isAuthenticated: false,
            sessionStatus: 'disconnected'
        };
    }
    /**
     * Inicializar conexão com WhatsApp
     */
    async initialize() {
        if (this.isInitializing) {
            console.log('⏳ Inicialização já em andamento...');
            return false;
        }
        this.isInitializing = true;
        try {
            console.log('🚀 Iniciando integração wppConnect...');
            const config = {
                session: 'rafaela-audio-session',
                headless: true,
                devtools: false,
                useChrome: false, // MUDANÇA: Usar Chromium padrão em vez de Chrome
                debug: false,
                logQR: true,
                autoClose: 0,
                puppeteerOptions: {
                    userDataDir: './tokens/rafaela-audio',
                    args: [
                        '--no-sandbox',
                        '--disable-setuid-sandbox',
                        '--disable-dev-shm-usage',
                        '--disable-accelerated-2d-canvas',
                        '--no-first-run',
                        '--no-zygote',
                        '--disable-gpu',
                        '--disable-web-security',
                        '--disable-features=VizDisplayCompositor',
                        '--single-process', // ADICIONADO: Força processo único
                        '--no-default-browser-check',
                        '--disable-default-apps',
                        '--disable-extensions',
                        '--disable-plugins',
                        '--disable-translate',
                        '--disable-background-networking',
                        '--disable-sync'
                    ],
                }
            };
            this.client = await wppconnect.create({
                ...config,
                catchQR: this.handleQRCode.bind(this),
                statusFind: this.handleStatusChange.bind(this),
                onLoadingScreen: this.handleLoadingScreen.bind(this)
            });
            if (this.client) {
                await this.setupEventListeners();
                await this.initializeAutomaticHandler();
                this.startKeepAlive(); // ATUALIZADO: Inicia o keep-alive para estabilidade
                console.log('✅ wppConnect integração inicializada com sucesso!');
                this.isInitializing = false;
                return true;
            }
            throw new Error('Cliente wppConnect não foi criado');
        }
        catch (error) {
            console.error('❌ Erro ao inicializar wppConnect:', error);
            this.connectionStatus.sessionStatus = 'error';
            this.isInitializing = false;
            this.scheduleReconnect();
            return false;
        }
    }
    /**
     * Configurar listeners de eventos
     */
    async setupEventListeners() {
        if (!this.client)
            return;
        console.log('🎧 Configurando listeners de eventos...');
        this.client.onMessage(async (message) => {
            try {
                await this.handleIncomingMessage(message);
            }
            catch (error) {
                console.error('❌ Erro ao processar mensagem:', error);
            }
        });
        this.client.onStateChange((state) => {
            console.log('📱 Estado alterado:', state);
            this.connectionStatus.sessionStatus = state;
            this.connectionStatus.lastActivity = new Date();
        });
        this.client.onAck((ack) => {
            // Opcional: Descomente para logs muito detalhados de ACK
            // console.log('✅ ACK recebido:', ack.id, ack.ack);
        });
        this.client.onPresenceChanged((presence) => {
            // Opcional: Descomente para logs de presença
            // console.log('👤 Presença alterada:', presence.id, presence.isOnline);
        });
        console.log('✅ Listeners configurados com sucesso');
    }
    /**
     * NOVO: Iniciar verificação periódica para manter a sessão ativa
     */
    startKeepAlive() {
        if (this.keepAliveInterval) {
            clearInterval(this.keepAliveInterval);
        }
        this.keepAliveInterval = setInterval(async () => {
            if (this.client && this.connectionStatus.isConnected) {
                try {
                    // Chama uma função leve para manter a sessão ativa
                    const battery = await this.client.getBatteryLevel();
                    console.log(`🔋 Keep-alive: Sessão ativa. Bateria do celular: ${battery}%`);
                }
                catch (error) {
                    console.warn('⚠️ Erro no keep-alive (pode indicar desconexão, tentando reconectar):', error);
                    this.scheduleReconnect();
                }
            }
        }, 4 * 60 * 1000); // A cada 4 minutos
    }
    /**
     * Processar mensagem recebida
     */
    async handleIncomingMessage(message) {
        try {
            if (message.fromMe) {
                return;
            }
            this.connectionStatus.lastActivity = new Date();
            console.log('📨 Mensagem recebida:', {
                from: message.from,
                type: message.type,
                id: message.id,
                timestamp: new Date(message.timestamp * 1000).toISOString(),
                hasBody: !!message.body,
                hasMedia: !!message.media
            });
            // Salvar mensagem no histórico primeiro (para todas as mensagens válidas)
            if (message.body || message.media) {
                await this.saveMessageToHistory(message);
            }
            // PRIORIDADE PARA ÁUDIO - Verificar múltiplos tipos
            if (message.type === 'ptt' || message.type === 'audio' || message.type === 'voice') {
                console.log('🎵 ÁUDIO DETECTADO! Processando...');
                await this.processAudioMessage(message);
            }
            else if (message.type === 'chat' && message.body) {
                console.log('💬 TEXTO DETECTADO! Processando...');
                await this.processTextMessage(message);
            }
            else {
                console.log(`📝 Tipo de mensagem não processado: ${message.type}`, {
                    hasBody: !!message.body,
                    hasMedia: !!message.media,
                    mimetype: message.mimetype
                });
            }
        }
        catch (error) {
            console.error('❌ Erro ao processar mensagem recebida:', error);
        }
    }
    /**
     * Processar mensagem de áudio
     */
    async processAudioMessage(message) {
        try {
            console.log('🎵 INICIANDO PROCESSAMENTO DE ÁUDIO...');
            console.log('📋 Detalhes da mensagem de áudio:', {
                type: message.type,
                id: message.id,
                from: message.from,
                hasMedia: !!message.media,
                mimetype: message.mimetype
            });
            // Sistema de áudio removido (simplificado)
            console.log('🎵 Áudio recebido mas processamento foi simplificado');
            // Fallback: processamento direto
            console.warn('🔄 Usando processamento direto de áudio...');
            await this.processAudioDirect(message);
        }
        catch (error) {
            console.error('❌ ERRO CRÍTICO no processamento de áudio:', error);
            await this.sendErrorMessage(message.from, '🎵 Desculpe, tive problemas para processar seu áudio. Tente enviar novamente ou digite sua mensagem.');
        }
    }
    /**
     * Processar áudio diretamente (fallback)
     */
    async processAudioDirect(message) {
        try {
            const audioResult = await this.downloadAudioWithRetry(message);
            if (!audioResult) {
                throw new Error('Não foi possível baixar o áudio após múltiplas tentativas');
            }
            const { buffer: audioBuffer, mimeType } = audioResult;
            console.log('📥 Áudio baixado (fallback):', {
                size: audioBuffer.length,
                mimeType,
                messageId: message.id
            });
            // Sistema de áudio removido (simplificado)
            console.log(`🎵 Áudio recebido mas processamento foi simplificado`);
            // Removido: mensagem de status desnecessária - a resposta final já terá simulação de digitação
        }
        catch (error) {
            console.error('❌ Erro no processamento direto de áudio:', error);
            throw error;
        }
    }
    /**
     * Baixar áudio com múltiplas tentativas - VERSÃO MELHORADA
     */
    async downloadAudioWithRetry(message, maxRetries = 3) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                console.log(`📥 [wppConnect] Tentativa ${attempt}/${maxRetries} de download do áudio...`);
                console.log(`🔍 [wppConnect] Dados da mensagem:`, {
                    id: message.id,
                    type: message.type,
                    hasMedia: !!message.media,
                    from: message.from,
                    mimetype: message.mimetype
                });
                // Método 1: downloadMedia
                let audioData = null;
                try {
                    audioData = await this.client.downloadMedia(message);
                    console.log(`📥 [wppConnect] downloadMedia resultado:`, {
                        type: typeof audioData,
                        isNull: audioData === null,
                        length: (audioData === null || audioData === void 0 ? void 0 : audioData.length) || 'N/A'
                    });
                }
                catch (downloadError) {
                    console.warn(`⚠️ [wppConnect] downloadMedia falhou:`, downloadError instanceof Error ? downloadError.message : downloadError);
                }
                // Método 2: Tentar métodos alternativos se necessário
                if (!audioData || (typeof audioData === 'string' && audioData.length < 100)) {
                    console.log(`🔄 [wppConnect] Tentando métodos alternativos...`);
                    try {
                        if (this.client.decryptFile && message.deprecatedMms3Url) {
                            audioData = await this.client.decryptFile(message);
                        }
                        else if (this.client.getMessageMedia) {
                            audioData = await this.client.getMessageMedia(message.id);
                        }
                    }
                    catch (altError) {
                        console.warn(`⚠️ [wppConnect] Métodos alternativos falharam:`, altError instanceof Error ? altError.message : altError);
                    }
                }
                if (!audioData) {
                    console.warn(`⚠️ [wppConnect] Tentativa ${attempt}: Nenhum método funcionou`);
                    continue;
                }
                let audioBuffer;
                let mimeType = message.mimetype || 'audio/ogg';
                // Processar dados do áudio
                if (typeof audioData === 'string') {
                    console.log(`🔍 [wppConnect] String recebida, tamanho: ${audioData.length}`);
                    console.log(`🔍 [wppConnect] Primeiros 100 chars:`, audioData.substring(0, 100));
                    if (audioData.length < 100) {
                        console.warn(`⚠️ [wppConnect] String muito pequena (${audioData.length} chars)`);
                        continue;
                    }
                    // Verificar se é data URL e extrair apenas o base64
                    let base64Data = audioData;
                    if (audioData.startsWith('data:')) {
                        const base64Index = audioData.indexOf('base64,');
                        if (base64Index !== -1) {
                            base64Data = audioData.substring(base64Index + 7); // Remove "base64,"
                            console.log(`🔧 [wppConnect] Removido prefixo data URL, novo tamanho: ${base64Data.length}`);
                        }
                    }
                    try {
                        audioBuffer = Buffer.from(base64Data, 'base64');
                        console.log(`✅ [wppConnect] Convertido de base64: ${audioBuffer.length} bytes`);
                        // Verificar se o buffer resultante é válido
                        if (audioBuffer.length < 1000) {
                            console.warn(`⚠️ [wppConnect] Buffer muito pequeno: ${audioBuffer.length} bytes`);
                            continue;
                        }
                    }
                    catch (error) {
                        console.warn(`⚠️ [wppConnect] Erro ao converter base64:`, error);
                        audioBuffer = Buffer.from(audioData);
                    }
                }
                else if (Buffer.isBuffer(audioData)) {
                    audioBuffer = audioData;
                    console.log(`✅ [wppConnect] Buffer direto: ${audioBuffer.length} bytes`);
                }
                else if (Array.isArray(audioData)) {
                    audioBuffer = Buffer.from(audioData);
                    console.log(`✅ [wppConnect] Array convertido: ${audioBuffer.length} bytes`);
                }
                else if (audioData && typeof audioData === 'object') {
                    if (audioData.data) {
                        audioBuffer = Buffer.from(audioData.data, 'base64');
                        mimeType = audioData.mimetype || mimeType;
                        console.log(`✅ [wppConnect] Objeto com data: ${audioBuffer.length} bytes, tipo: ${mimeType}`);
                    }
                    else {
                        console.warn(`⚠️ [wppConnect] Objeto sem propriedade 'data':`, Object.keys(audioData));
                        continue;
                    }
                }
                else {
                    console.warn(`⚠️ [wppConnect] Formato inesperado:`, typeof audioData);
                    continue;
                }
                // Validar tamanho mínimo
                if (audioBuffer.length < 1000) {
                    console.warn(`⚠️ [wppConnect] Áudio muito pequeno: ${audioBuffer.length} bytes`);
                    continue;
                }
                console.log(`✅ [wppConnect] Áudio baixado com sucesso: ${audioBuffer.length} bytes`);
                return { buffer: audioBuffer, mimeType };
            }
            catch (error) {
                console.error(`❌ Erro na tentativa ${attempt} de download:`, error);
                if (attempt === maxRetries)
                    throw error;
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        }
        return null;
    }
    /**
     * Processar mensagem de texto
     */
    async processTextMessage(message) {
        try {
            console.log('💬 Processando mensagem de texto...');
            const contact = await this.getOrCreateContact(message.from);
            await this.saveMessage(message, contact);
            if (contact.registration_status === 'registered') {
                const iContact = this.convertToIContact(contact);
                const aiResponse = await this.geminiService.generateResponse(iContact, message.body);
                await this.sendText(message.from, aiResponse.response);
                console.log('✅ Resposta de texto enviada');
            }
        }
        catch (error) {
            console.error('❌ Erro ao processar mensagem de texto:', error);
        }
    }
    /**
     * Inicializar handler automático
     */
    async initializeAutomaticHandler() {
        if (!this.client)
            return;
        try {
            // Audio handler removido (sistema simplificado)
            // Audio handler removido (sistema simplificado)
            // REMOVIDO: Listener duplicado - o whatsappAudioHandler já cuida do envio
            // audioQueue.on('responseReady', async (data) => {
            //   try {
            //     await this.sendText(data.phoneNumber, data.response);
            //     console.log(`✅ Resposta automática enviada para: ${data.phoneNumber}`);
            //   } catch (error) {
            //     console.error('❌ Erro ao enviar resposta automática da fila:', error);
            //   }
            // });
            console.log('🤖 Handler automático inicializado e ouvindo a fila');
        }
        catch (error) {
            console.error('❌ Erro ao inicializar handler automático:', error);
        }
    }
    /**
     * Verificar status do número usando wa-js
     */
    async checkNumberStatus(contactId) {
        try {
            if (!this.client || !this.connectionStatus.isConnected) {
                throw new Error('Cliente não conectado');
            }
            // Remove caracteres não numéricos e prepara o número
            const cleanPhone = contactId.replace(/\D/g, '');
            // Tenta diferentes formatos brasileiros
            const possibleFormats = [
                `55${cleanPhone}@c.us`, // Com código do país
                `${cleanPhone}@c.us`, // Sem código do país
                `55${cleanPhone.substring(2)}@c.us` // Remove DDD duplicado se houver
            ];
            for (const format of possibleFormats) {
                try {
                    console.log(`🔍 Verificando número: ${format}`);
                    const result = await this.client.checkNumberStatus(format);
                    if (result && result.numberExists) {
                        console.log(`✅ Número válido encontrado: ${result.id._serialized}`);
                        return {
                            id: result.id._serialized,
                            isBusiness: result.isBusiness || false,
                            canReceiveMessage: true,
                            numberExists: true,
                            status: 200
                        };
                    }
                }
                catch (error) {
                    console.log(`❌ Formato ${format} inválido:`, error instanceof Error ? error.message : error);
                    continue;
                }
            }
            // Se nenhum formato funcionou
            return {
                id: contactId,
                isBusiness: false,
                canReceiveMessage: false,
                numberExists: false,
                status: 404
            };
        }
        catch (error) {
            console.error('❌ Erro ao verificar status do número:', error);
            return {
                id: contactId,
                isBusiness: false,
                canReceiveMessage: false,
                numberExists: false,
                status: 500
            };
        }
    }
    /**
     * Simular digitação (typing indicator)
     */
    async startTyping(to) {
        try {
            if (!this.client || !this.connectionStatus.isConnected) {
                return;
            }
            await this.client.startTyping(to);
            console.log(`⌨️ Iniciando indicador de digitação para: ${to}`);
        }
        catch (error) {
            console.warn('⚠️ Erro ao iniciar digitação:', error instanceof Error ? error.message : error);
        }
    }
    /**
     * Parar digitação (typing indicator)
     */
    async stopTyping(to) {
        try {
            if (!this.client || !this.connectionStatus.isConnected) {
                return;
            }
            await this.client.stopTyping(to);
            console.log(`⌨️ Parando indicador de digitação para: ${to}`);
        }
        catch (error) {
            console.warn('⚠️ Erro ao parar digitação:', error instanceof Error ? error.message : error);
        }
    }
    /**
     * Enviar mensagem de texto com simulação de digitação
     */
    async sendText(to, message, simulateTyping = true) {
        try {
            if (!this.client || !this.connectionStatus.isConnected) {
                throw new Error('Cliente não conectado para enviar mensagem');
            }
            console.log(`📞 Validando número: ${to}`);
            // Verificar e validar o número usando wa-js
            const numberStatus = await this.checkNumberStatus(to);
            if (!numberStatus.numberExists || !numberStatus.canReceiveMessage) {
                throw new Error(`Número ${to} não existe ou não pode receber mensagens`);
            }
            console.log(`✅ Número validado: ${to} → ${numberStatus.id}`);
            if (simulateTyping) {
                // Iniciar indicador de digitação
                await this.startTyping(numberStatus.id);
                // Delay baseado no tamanho da mensagem (mais realista)
                const typingDelay = Math.min(Math.max(message.length * 50, 2000), 5000); // Entre 2s e 5s
                console.log(`⏱️ Simulando digitação por ${typingDelay}ms para mensagem de ${message.length} caracteres...`);
                await new Promise(resolve => setTimeout(resolve, typingDelay));
                // Parar indicador de digitação
                await this.stopTyping(numberStatus.id);
            }
            // Usar o ID corrigido pelo WhatsApp
            await this.client.sendText(numberStatus.id, message);
            console.log(`📤 Mensagem enviada para ${numberStatus.id}`);
            return true;
        }
        catch (error) {
            console.error(`❌ Erro ao enviar mensagem para ${to}:`, error);
            return false;
        }
    }
    async sendErrorMessage(to, errorMsg) {
        try {
            await this.sendText(to, errorMsg);
        }
        catch (error) {
            console.error('❌ Erro ao enviar mensagem de erro:', error);
        }
    }
    /**
     * Handlers de eventos wppConnect
     */
    handleQRCode(_base64Qr, asciiQR, attempts) {
        console.log(`\n🔲 ===== QR CODE PARA WHATSAPP (Tentativa: ${attempts}) =====`);
        console.log(asciiQR || 'QR Code gerado - Escaneie com WhatsApp');
        console.log('===================================================\n');
        this.connectionStatus.sessionStatus = 'qr_ready';
    }
    handleStatusChange(statusSession, session) {
        console.log(`📱 Status: ${statusSession} | Sessão: ${session}`);
        this.connectionStatus.sessionStatus = statusSession;
        switch (statusSession) {
            case 'isLogged':
                this.connectionStatus.isConnected = true;
                this.connectionStatus.isAuthenticated = true;
                this.reconnectAttempts = 0;
                console.log('✅ WhatsApp conectado e autenticado!');
                this.getDeviceInfo();
                break;
            case 'notLogged':
            case 'browserClose':
            case 'autocloseCalled':
                this.connectionStatus.isConnected = false;
                this.connectionStatus.isAuthenticated = false;
                console.log(`❌ WhatsApp desconectado (Motivo: ${statusSession}). Agendando reconexão...`);
                this.scheduleReconnect();
                break;
            case 'inChat':
                this.connectionStatus.isConnected = true;
                console.log('💬 Em chat - Pronto para receber mensagens!');
                break;
        }
    }
    handleLoadingScreen(percent, message) {
        console.log(`⏳ Carregando: ${percent}% - ${message}`);
    }
    async getDeviceInfo() {
        var _a, _b;
        try {
            if (this.client) {
                const info = await this.client.getHostDevice();
                this.connectionStatus.deviceInfo = info;
                this.connectionStatus.phoneNumber = (_a = info.id) === null || _a === void 0 ? void 0 : _a.user;
                console.log('📱 Dispositivo conectado:', { phone: (_b = info.id) === null || _b === void 0 ? void 0 : _b.user, name: info.pushname, platform: info.platform });
            }
        }
        catch (error) {
            console.error('❌ Erro ao obter info do dispositivo:', error);
        }
    }
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('💀 Máximo de tentativas de reconexão atingido. Verifique o problema manualmente.');
            return;
        }
        const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 60000);
        this.reconnectAttempts++;
        console.log(`🔄 Agendando reconexão em ${delay / 1000}s (tentativa ${this.reconnectAttempts})`);
        if (this.reconnectInterval)
            clearTimeout(this.reconnectInterval);
        this.reconnectInterval = setTimeout(() => this.initialize(), delay);
    }
    /**
     * Métodos auxiliares
     */
    async getOrCreateContact(whatsappPhone) {
        const brazilianPhone = this.normalizePhoneToBrazilian(whatsappPhone);
        let contact = await contactService_1.contactService.findByPhone(brazilianPhone);
        if (!contact) {
            contact = await contactService_1.contactService.create({
                phone: brazilianPhone,
                name: `Contato ${brazilianPhone.slice(-4)}`,
                last_interaction: new Date().toISOString(),
                is_active: true,
                registration_status: 'unregistered',
                evaluation_messages: 0,
                interest_score: 0
            });
        }
        return contact;
    }
    normalizePhoneToBrazilian(whatsappPhone) {
        let phone = whatsappPhone.replace('@c.us', '');
        if (phone.startsWith('55'))
            phone = phone.substring(2);
        if (phone.length === 11) {
            const ddd = phone.substring(0, 2);
            const nono = phone.substring(2, 3);
            const primeiros4 = phone.substring(3, 7);
            const ultimos4 = phone.substring(7, 11);
            return `(${ddd}) ${nono}${primeiros4}-${ultimos4}`;
        }
        return phone;
    }
    convertToIContact(contact) {
        return {
            name: contact.name, phone: contact.phone, babyGender: contact.baby_gender || 'unknown',
            isActive: contact.is_active, lastInteraction: new Date(contact.last_interaction),
            registrationStatus: contact.registration_status, evaluationMessages: contact.evaluation_messages,
            interestScore: contact.interest_score,
            evaluationStartDate: contact.evaluation_start_date ? new Date(contact.evaluation_start_date) : undefined,
            createdAt: new Date(contact.created_at || ''), updatedAt: new Date(contact.updated_at || ''),
            updateInteraction: async () => { }, incrementEvaluationMessages: async () => { }, updateInterestScore: async () => { },
            markAsRegistered: async () => { }, markAsNotInterested: async () => { }, deactivate: async () => { }, reactivate: async () => { }
        };
    }
    /**
     * Salvar mensagem no histórico (nova versão para interface WhatsApp & IA)
     */
    async saveMessageToHistory(message) {
        try {
            console.log('💾 Salvando mensagem no histórico...');
            // Buscar ou criar contato
            const contact = await this.getOrCreateContact(message.from);
            // Determinar conteúdo da mensagem
            let content = message.body || '[MENSAGEM SEM TEXTO]';
            if (message.type === 'ptt' || message.type === 'audio' || message.type === 'voice') {
                content = '🎵 [MENSAGEM DE ÁUDIO]';
            }
            else if (message.media && !message.body) {
                content = '📎 [MÍDIA]';
            }
            // Salvar no banco via API interna
            if (contact.id) {
                const messageData = {
                    contact_id: contact.id,
                    content,
                    type: message.type,
                    from_me: false, // Mensagem recebida
                    timestamp: new Date(message.timestamp * 1000).toISOString(),
                    message_id: message.id,
                    status: 'delivered'
                };
                await messageService_1.messageService.create(messageData);
            }
            console.log(`✅ Mensagem salva no histórico para ${contact.name}`);
        }
        catch (error) {
            console.error('❌ Erro ao salvar mensagem no histórico:', error);
        }
    }
    async saveMessage(message, contact) {
        try {
            if (contact.id) {
                await messageService_1.messageService.create({
                    contact_id: contact.id,
                    content: message.body || '[MENSAGEM SEM TEXTO]',
                    type: message.type,
                    from_me: message.fromMe,
                    timestamp: new Date(message.timestamp * 1000).toISOString(),
                    message_id: message.id,
                    status: 'delivered'
                });
            }
        }
        catch (error) {
            console.error('❌ Erro ao salvar mensagem:', error);
        }
    }
    /**
     * Métodos públicos para controle
     */
    getConnectionStatus() {
        return { ...this.connectionStatus };
    }
    getStats() {
        return {
            connection: this.connectionStatus,
            automaticHandler: null, // Removido
            audioQueue: null, // Removido
            reconnectAttempts: this.reconnectAttempts
        };
    }
    async disconnect() {
        try {
            if (this.reconnectInterval)
                clearTimeout(this.reconnectInterval);
            if (this.keepAliveInterval)
                clearInterval(this.keepAliveInterval); // ATUALIZADO
            this.reconnectInterval = null;
            this.keepAliveInterval = null; // ATUALIZADO
            // Audio handler removido
            if (this.client) {
                await this.client.close();
                this.client = null;
            }
            this.connectionStatus = { isConnected: false, isAuthenticated: false, sessionStatus: 'disconnected' };
            console.log('🛑 wppConnect desconectado');
        }
        catch (error) {
            console.error('❌ Erro ao desconectar:', error);
        }
    }
}
exports.WppConnectIntegration = WppConnectIntegration;
// Instância singleton
exports.wppConnectIntegration = new WppConnectIntegration(new gemini_1.GeminiAIService());
