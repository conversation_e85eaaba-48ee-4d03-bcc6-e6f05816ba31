"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.messageService = exports.MessageService = void 0;
const supabase_1 = require("../config/supabase");
class MessageService {
    // Buscar todas as mensagens
    async findAll(filters = {}) {
        try {
            let query = supabase_1.supabase
                .from('messages')
                .select('*')
                .order('timestamp', { ascending: false });
            // Aplicar filtros
            if (filters.contactId) {
                query = query.eq('contact_id', filters.contactId);
            }
            if (filters.fromMe !== undefined) {
                query = query.eq('from_me', filters.fromMe);
            }
            if (filters.limit) {
                query = query.limit(filters.limit);
            }
            if (filters.offset) {
                query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
            }
            const { data, error } = await query;
            if (error) {
                console.error('❌ Erro ao buscar mensagens:', error);
                throw new Error(`Erro ao buscar mensagens: ${error.message}`);
            }
            return data || [];
        }
        catch (error) {
            console.error('❌ Erro no MessageService.findAll:', error);
            throw error;
        }
    }
    // Buscar mensagem por ID
    async findById(id) {
        try {
            const { data, error } = await supabase_1.supabase
                .from('messages')
                .select('*')
                .eq('id', id)
                .single();
            if (error) {
                if (error.code === 'PGRST116') {
                    return null; // Não encontrado
                }
                console.error('❌ Erro ao buscar mensagem por ID:', error);
                throw new Error(`Erro ao buscar mensagem: ${error.message}`);
            }
            return data;
        }
        catch (error) {
            console.error('❌ Erro no MessageService.findById:', error);
            throw error;
        }
    }
    // Buscar mensagens por contato
    async findByContactId(contactId, limit = 50) {
        try {
            const { data, error } = await supabase_1.supabase
                .from('messages')
                .select('*')
                .eq('contact_id', contactId)
                .order('timestamp', { ascending: false })
                .limit(limit);
            if (error) {
                console.error('❌ Erro ao buscar mensagens por contato:', error);
                throw new Error(`Erro ao buscar mensagens: ${error.message}`);
            }
            return data || [];
        }
        catch (error) {
            console.error('❌ Erro no MessageService.findByContactId:', error);
            throw error;
        }
    }
    // Criar nova mensagem
    async create(messageData) {
        try {
            console.log('💾 Criando nova mensagem no Supabase...');
            // Verificar se a mensagem já existe pelo message_id
            if (messageData.message_id) {
                const existingMessage = await this.findByMessageId(messageData.message_id);
                if (existingMessage) {
                    console.log('⚠️ Mensagem já existe, retornando existente:', messageData.message_id);
                    return existingMessage;
                }
            }
            const { data, error } = await supabase_1.supabase
                .from('messages')
                .insert([messageData])
                .select()
                .single();
            if (error) {
                // Se ainda assim der erro de chave duplicada, tentar buscar a mensagem existente
                if (error.code === '23505' && messageData.message_id) {
                    console.log('⚠️ Chave duplicada detectada, buscando mensagem existente...');
                    const existingMessage = await this.findByMessageId(messageData.message_id);
                    if (existingMessage) {
                        return existingMessage;
                    }
                }
                console.error('❌ Erro ao criar mensagem:', error);
                throw new Error(`Erro ao criar mensagem: ${error.message}`);
            }
            console.log('✅ Mensagem criada com sucesso');
            return data;
        }
        catch (error) {
            console.error('❌ Erro no MessageService.create:', error);
            throw error;
        }
    }
    // Atualizar mensagem
    async update(id, updates) {
        try {
            const { data, error } = await supabase_1.supabase
                .from('messages')
                .update(updates)
                .eq('id', id)
                .select()
                .single();
            if (error) {
                console.error('❌ Erro ao atualizar mensagem:', error);
                throw new Error(`Erro ao atualizar mensagem: ${error.message}`);
            }
            console.log('✅ Mensagem atualizada com sucesso');
            return data;
        }
        catch (error) {
            console.error('❌ Erro no MessageService.update:', error);
            throw error;
        }
    }
    // Deletar mensagem
    async delete(id) {
        try {
            const { error } = await supabase_1.supabase
                .from('messages')
                .delete()
                .eq('id', id);
            if (error) {
                console.error('❌ Erro ao deletar mensagem:', error);
                throw new Error(`Erro ao deletar mensagem: ${error.message}`);
            }
            console.log('✅ Mensagem deletada com sucesso');
            return true;
        }
        catch (error) {
            console.error('❌ Erro no MessageService.delete:', error);
            throw error;
        }
    }
    // Contar mensagens
    async count(filters = {}) {
        try {
            let query = supabase_1.supabase
                .from('messages')
                .select('*', { count: 'exact', head: true });
            if (filters.contactId) {
                query = query.eq('contact_id', filters.contactId);
            }
            if (filters.fromMe !== undefined) {
                query = query.eq('from_me', filters.fromMe);
            }
            const { count, error } = await query;
            if (error) {
                console.error('❌ Erro ao contar mensagens:', error);
                throw new Error(`Erro ao contar mensagens: ${error.message}`);
            }
            return count || 0;
        }
        catch (error) {
            console.error('❌ Erro no MessageService.count:', error);
            throw error;
        }
    }
    // Contar mensagens de hoje
    async countToday() {
        try {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const { count, error } = await supabase_1.supabase
                .from('messages')
                .select('*', { count: 'exact', head: true })
                .gte('timestamp', today.toISOString());
            if (error) {
                console.error('❌ Erro ao contar mensagens de hoje:', error);
                throw new Error(`Erro ao contar mensagens: ${error.message}`);
            }
            return count || 0;
        }
        catch (error) {
            console.error('❌ Erro no MessageService.countToday:', error);
            throw error;
        }
    }
    // Buscar mensagens por período
    async findByDateRange(startDate, endDate) {
        try {
            const { data, error } = await supabase_1.supabase
                .from('messages')
                .select('*')
                .gte('timestamp', startDate.toISOString())
                .lte('timestamp', endDate.toISOString())
                .order('timestamp', { ascending: false });
            if (error) {
                console.error('❌ Erro ao buscar mensagens por período:', error);
                throw new Error(`Erro ao buscar mensagens: ${error.message}`);
            }
            return data || [];
        }
        catch (error) {
            console.error('❌ Erro no MessageService.findByDateRange:', error);
            throw error;
        }
    }
    // Buscar última mensagem de um contato
    async findLastByContactId(contactId) {
        try {
            const { data, error } = await supabase_1.supabase
                .from('messages')
                .select('*')
                .eq('contact_id', contactId)
                .order('timestamp', { ascending: false })
                .limit(1)
                .single();
            if (error) {
                if (error.code === 'PGRST116') {
                    return null; // Não encontrado
                }
                console.error('❌ Erro ao buscar última mensagem:', error);
                throw new Error(`Erro ao buscar mensagem: ${error.message}`);
            }
            return data;
        }
        catch (error) {
            console.error('❌ Erro no MessageService.findLastByContactId:', error);
            throw error;
        }
    }
    // Buscar mensagem por message_id do WhatsApp
    async findByMessageId(messageId) {
        try {
            const { data, error } = await supabase_1.supabase
                .from('messages')
                .select('*')
                .eq('message_id', messageId)
                .single();
            if (error) {
                if (error.code === 'PGRST116') {
                    return null; // Não encontrado
                }
                console.error('❌ Erro ao buscar mensagem por message_id:', error);
                throw new Error(`Erro ao buscar mensagem: ${error.message}`);
            }
            return data;
        }
        catch (error) {
            console.error('❌ Erro no MessageService.findByMessageId:', error);
            return null; // Retorna null em caso de erro para não quebrar o fluxo
        }
    }
    // Atualizar sentimento da mensagem
    async updateSentiment(id, sentiment) {
        try {
            const { error } = await supabase_1.supabase
                .from('messages')
                .update({ sentiment })
                .eq('id', id);
            if (error) {
                console.error('❌ Erro ao atualizar sentimento:', error);
                throw new Error(`Erro ao atualizar sentimento: ${error.message}`);
            }
            console.log('✅ Sentimento atualizado com sucesso');
        }
        catch (error) {
            console.error('❌ Erro no MessageService.updateSentiment:', error);
            throw error;
        }
    }
}
exports.MessageService = MessageService;
exports.messageService = new MessageService();
exports.default = exports.messageService;
