import { supabase } from '../config/supabase';

/**
 * Script para corrigir o formato do telefone de um contato
 */

async function fixPhoneFormat(contactId: string, newPhone: string) {
  console.log(`🔄 Atualizando telefone do contato ${contactId} para '${newPhone}'...`);
  
  try {
    const { data, error } = await supabase
      .from('contacts')
      .update({ 
        phone: newPhone,
        last_interaction: new Date().toISOString()
      })
      .eq('id', contactId)
      .select()
      .single();

    if (error) {
      console.error('❌ Erro ao atualizar contato:', error);
      throw error;
    }

    console.log('✅ Telefone atualizado com sucesso:', {
      id: data.id,
      name: data.name,
      phone: data.phone,
      registration_status: data.registration_status
    });

    return data;

  } catch (error) {
    console.error('❌ Erro durante a atualização:', error);
    throw error;
  }
}

// Executar o script se chamado diretamente
if (require.main === module) {
  const contactId = process.argv[2];
  const newPhone = process.argv[3];
  
  if (!contactId || !newPhone) {
    console.error('❌ Uso: npx ts-node fixPhoneFormat.ts <contactId> <newPhone>');
    console.error('   Exemplo: npx ts-node fixPhoneFormat.ts abc123 "(84) 98850-1582"');
    process.exit(1);
  }

  fixPhoneFormat(contactId, newPhone)
    .then(() => {
      console.log('🏁 Script finalizado com sucesso');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Falha na atualização:', error);
      process.exit(1);
    });
}

export { fixPhoneFormat };
