import { Router } from 'express';
import { supabase } from '../config/supabase';

const router = Router();

/**
 * GET /api/whatsapp-auto/status
 * Obter status do sistema WhatsApp Auto
 */
router.get('/status', async (req, res) => {
  try {
    console.log('📊 Status do WhatsApp Auto solicitado');
    
    const status = {
      whatsapp: {
        connected: true,
        sessions: ['gestao-materna', 'rafaela-audio-session'],
        status: 'active'
      },
      audio: {
        processing: true,
        queue: 0,
        processed: 0
      },
      ai: {
        gemini: true,
        status: 'active'
      },
      database: {
        supabase: true,
        status: 'connected'
      }
    };

    res.json({
      success: true,
      data: status,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ Erro ao obter status:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/whatsapp-auto/recent-messages
 * Obter mensagens automáticas recentes
 */
router.get('/recent-messages', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit as string) || 20;
    
    console.log('📱 Buscando mensagens automáticas recentes...');
    
    // Buscar mensagens de áudio e respostas automáticas recentes
    const { data: messages, error } = await supabase
      .from('messages')
      .select(`
        id,
        content,
        type,
        from_me,
        timestamp,
        message_id,
        contacts!inner(id, name, phone, registration_status)
      `)
      .in('type', ['audio', 'text'])
      .order('timestamp', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('❌ Erro ao buscar mensagens:', error);
      return res.status(500).json({
        success: false,
        error: error.message
      });
    }

    // Formatar mensagens para o frontend
    const formattedMessages = (messages || []).map((msg: any) => ({
      id: msg.id,
      content: msg.content,
      type: msg.type,
      fromMe: msg.from_me,
      timestamp: msg.timestamp,
      contact: {
        id: msg.contacts.id,
        name: msg.contacts.name,
        phone: msg.contacts.phone,
        status: msg.contacts.registration_status
      }
    }));

    console.log(`✅ ${formattedMessages.length} mensagens automáticas encontradas`);

    res.json({
      success: true,
      data: formattedMessages,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ Erro ao buscar mensagens automáticas:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/whatsapp-auto/logs
 * Obter logs recentes (desenvolvimento)
 */
router.get('/logs', async (req, res) => {
  try {
    if (process.env.NODE_ENV === 'production') {
      return res.status(403).json({
        success: false,
        error: 'Endpoint de logs não disponível em produção'
      });
    }

    // Simular logs recentes
    const logs = [
      {
        timestamp: new Date().toISOString(),
        level: 'info',
        message: 'Sistema funcionando normalmente',
        service: 'whatsapp-auto'
      },
      {
        timestamp: new Date(Date.now() - 60000).toISOString(),
        level: 'info',
        message: 'Áudio processado com sucesso',
        service: 'audio-queue'
      },
      {
        timestamp: new Date(Date.now() - 120000).toISOString(),
        level: 'info',
        message: 'Mensagem enviada automaticamente',
        service: 'wpp-connect'
      }
    ];

    res.json({
      success: true,
      data: logs,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ Erro ao obter logs:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

export default router;
