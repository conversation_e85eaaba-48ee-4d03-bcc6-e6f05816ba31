"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeminiAIService = void 0;
const generative_ai_1 = require("@google/generative-ai");
// =========================================================
// NOVA CLASSE: Sistema de Templates Adaptativos
// =========================================================
class AdaptiveResponseSystem {
    static classifyMessage(message) {
        const msg = message.toLowerCase();
        if (msg.match(/^(oi|olá|bom dia|boa tarde|boa noite|tudo bem|como vai)/)) {
            return 'saudacao';
        }
        if (msg.includes('dor') && !msg.includes('forte') && !msg.includes('sangue')) {
            return 'dor_normal';
        }
        if (msg.match(/(exame|ultrassom|médico|consulta)/)) {
            return 'exames';
        }
        if (msg.match(/(mexendo|movimento|mexe|chutando|chute)/)) {
            return 'movimento_bebe';
        }
        if (msg.match(/(comer|comida|alimentação|fruta|verdura)/)) {
            return 'alimentacao';
        }
        return 'complexa';
    }
    static applyTemplate(contact, type) {
        const templates = this.templates[type];
        const template = templates[Math.floor(Math.random() * templates.length)];
        const babyGender = contact.babyGender === 'male' ? 'bebezinho' :
            contact.babyGender === 'female' ? 'bebezinha' : 'bebê';
        return template
            .replace('{nome}', contact.name)
            .replace('{bebe}', babyGender)
            .replace('{trimestre}', 'seu trimestre');
    }
    static detectUrgency(message) {
        const urgentKeywords = ['sangramento', 'dor forte', 'emergência', 'hospital', 'urgente', 'sangue', 'contracao'];
        return urgentKeywords.some(keyword => message.toLowerCase().includes(keyword));
    }
}
AdaptiveResponseSystem.templates = {
    saudacao: [
        "Oi {nome}! Como você e o {bebe} estão hoje? Seguimos firmes na nossa jornada! 🧡",
        "Que bom te ouvir, {nome}! Como anda nossa caminhada com o {bebe}? Deus abençoa vocês! 💪🏽",
        "Oi minha querida {nome}! E aí, como estão as coisas por aí? O {bebe} mexendo bastante? 😊"
    ],
    dor_normal: [
        "Entendo sua preocupação, {nome}. Essas dores são comuns no {trimestre}. O {bebe} está crescendo! Seguimos firmes. Mas se intensificar, procure seu médico.",
        "Minha querida, no {trimestre} é normal sentir isso. O {bebe} está se desenvolvendo bem! Relaxa, respira fundo. Deus cuida de vocês."
    ],
    exames: [
        "Que bom que está acompanhando direitinho, {nome}! Os exames do {trimestre} são importantes para você e o {bebe}. Seguimos firmes no cuidado!",
        "Parabéns por ser tão cuidadosa! No {trimestre}, esses exames mostram como o {bebe} está crescendo. Nossa gente precisa desse carinho todo."
    ],
    movimento_bebe: [
        "Que alegria, {nome}! O {bebe} mexendo é sinal de que está bem ativo e saudável! Seguimos firmes com essa benção. Como tem sido essa experiência?",
        "Ai que amor, {nome}! Sentir o {bebe} se mexer é puro amor divino. Deus abençoa essa conexão especial entre vocês duas!"
    ],
    alimentacao: [
        "Muito bem, {nome}! Cuidar da alimentação é cuidar do {bebe} também. Frutas, verduras e muito amor. Seguimos firmes na alimentação saudável!",
        "Perfeito, minha querida! O que você come, o {bebe} também recebe. Essa dedicação toda é linda de ver. Nossa gente cuidando direito!"
    ]
};
// =========================================================
// NOVA CLASSE: Otimizador de Prompts
// =========================================================
class PromptOptimizer {
    static generateOptimizedPrompt(contact, message, hasAudio = false, conversationSummary) {
        const cacheKey = `${contact.phone}_${contact.babyGender}`;
        let contactContext = this.contextCache.get(cacheKey);
        if (!contactContext) {
            contactContext = `${contact.name} | ${contact.babyGender === 'male' ? 'menino' :
                contact.babyGender === 'female' ? 'menina' : 'bebê'}`;
            this.contextCache.set(cacheKey, contactContext);
        }
        return `${this.basePersonality}

GESTANTE: ${contactContext}
${conversationSummary ? `HISTÓRICO: ${conversationSummary}` : ''}
MENSAGEM: ${message}${hasAudio ? ' (+ ÁUDIO)' : ''}

RESPONDA como Rafaela:`;
    }
    static generateHybridPrompt(contact, message, hasAudio = false, conversationSummary) {
        const basePrompt = this.generateOptimizedPrompt(contact, message, hasAudio, conversationSummary);
        return `${basePrompt}

FORMATO DE RESPOSTA:
1. RESPOSTA_RAFAELA: [sua resposta natural de 40-60 palavras]
2. ANÁLISE_JSON: {"sentiment":"positive/negative/neutral","urgency":"alta/media/baixa","needs":["lista"],"medical":true/false}

EXEMPLO:
RESPOSTA_RAFAELA: Oi minha querida! Que bom saber de você. Essas dores são normais no segundo trimestre, mas seguimos firmes! Deus abençoa nossa caminhada. Como está se sentindo hoje?

ANÁLISE_JSON: {"sentiment":"neutral","urgency":"media","needs":["informação sobre dores","acompanhamento"],"medical":false}`;
    }
    static summarizeConversation(messages) {
        if (messages.length === 0)
            return '';
        const recentMessages = messages.slice(-3);
        const topics = new Set();
        recentMessages.forEach(msg => {
            const content = msg.content.toLowerCase();
            if (content.includes('dor'))
                topics.add('dor');
            if (content.includes('exame'))
                topics.add('exames');
            if (content.includes('bebê') || content.includes('bebe'))
                topics.add('bebê');
            if (content.includes('médico'))
                topics.add('médico');
            if (content.includes('preocupa'))
                topics.add('preocupação');
            if (content.includes('mexendo'))
                topics.add('movimento');
            if (content.includes('alimenta'))
                topics.add('alimentação');
        });
        return topics.size > 0 ? `Tópicos: ${Array.from(topics).join(', ')}` : '';
    }
}
PromptOptimizer.basePersonality = `Você é "Rafaela" - vereadora jovem de Parnamirim, fonoaudióloga cristã.
ESTILO: "nossa gente", "seguimos firmes", "minha querida" | FÉ: "Deus abençoa" | TOM: maternal, próxima
RESPOSTA: 40-60 palavras, concisa, carinhosa, informativa`;
PromptOptimizer.contextCache = new Map();
// =========================================================
// FUNÇÕES AUXILIARES MANTIDAS E OTIMIZADAS
// =========================================================
function cleanGeminiJSON(text, attempt = 1) {
    try {
        console.log(`🧹 Tentativa ${attempt} - Limpando JSON do Gemini:`, text.substring(0, 100) + '...');
        let cleaned = text;
        cleaned = cleaned.replace(/```json\s*/gi, '').replace(/```\s*/g, '');
        cleaned = cleaned.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
        cleaned = cleaned.trim();
        const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
            cleaned = jsonMatch[0];
        }
        if (!cleaned.startsWith('{') && cleaned.includes('[')) {
            const arrayMatch = cleaned.match(/\[[\s\S]*\]/);
            if (arrayMatch) {
                cleaned = arrayMatch[0];
            }
        }
        cleaned = cleaned
            .replace(/,\s*}/g, '}')
            .replace(/,\s*]/g, ']')
            .replace(/\n\s*\n/g, '\n')
            .replace(/\t/g, ' ')
            .replace(/\r/g, '');
        console.log(`✅ Tentativa ${attempt} - JSON limpo:`, cleaned.substring(0, 100) + '...');
        return cleaned;
    }
    catch (error) {
        console.error(`❌ Erro na tentativa ${attempt} ao limpar JSON do Gemini:`, error);
        return text;
    }
}
function parseJSONWithFallback(text) {
    const maxAttempts = 3;
    let lastError = null;
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
            const cleaned = cleanGeminiJSON(text, attempt);
            const parsed = JSON.parse(cleaned);
            console.log(`✅ JSON parseado com sucesso na tentativa ${attempt}`);
            return parsed;
        }
        catch (error) {
            lastError = error;
            console.warn(`⚠️ Tentativa ${attempt} falhou:`, error);
            if (attempt < maxAttempts) {
                if (attempt === 2) {
                    text = text.replace(/\\/g, '\\\\').replace(/"/g, '\\"');
                }
                else if (attempt === 3) {
                    const simpleMatch = text.match(/\{[^{}]*\}/);
                    if (simpleMatch) {
                        text = simpleMatch[0];
                    }
                }
            }
        }
    }
    console.error('❌ Todas as tentativas de parsing falharam');
    console.error('📄 Texto original completo:', text);
    console.error('📄 Último erro:', lastError === null || lastError === void 0 ? void 0 : lastError.message);
    throw new Error(`Falha ao fazer parse do JSON após ${maxAttempts} tentativas: ${lastError === null || lastError === void 0 ? void 0 : lastError.message}`);
}
function validateResponseLength(response, minWords = 15, maxWords = 60) {
    const words = response.trim().split(/\s+/);
    const wordCount = words.length;
    console.log(`📏 Resposta da IA: ${wordCount} palavras (meta: ${minWords}-${maxWords})`);
    if (wordCount >= minWords && wordCount <= maxWords) {
        console.log('✅ Tamanho da resposta adequado');
        return response;
    }
    if (wordCount > maxWords) {
        const text = response.trim();
        const sentences = text.split(/[.!?]+/);
        let truncated = '';
        let currentWords = 0;
        for (const sentence of sentences) {
            const sentenceWords = sentence.trim().split(/\s+/).length;
            if (currentWords + sentenceWords <= maxWords && sentence.trim()) {
                truncated += (truncated ? '. ' : '') + sentence.trim();
                currentWords += sentenceWords;
            }
            else {
                break;
            }
        }
        if (truncated && currentWords >= minWords) {
            console.log(`✂️ Resposta truncada inteligentemente de ${wordCount} para ${currentWords} palavras (frases completas)`);
            return truncated + (truncated.endsWith('.') || truncated.endsWith('!') || truncated.endsWith('?') ? '' : '.');
        }
        const wordsTruncated = words.slice(0, maxWords);
        let result = wordsTruncated.join(' ');
        if (result.endsWith(',') || result.endsWith(';')) {
            const lastSpaceIndex = result.lastIndexOf(' ');
            if (lastSpaceIndex > 0) {
                result = result.substring(0, lastSpaceIndex);
            }
        }
        console.log(`✂️ Resposta truncada de ${wordCount} para ${maxWords} palavras (fallback)`);
        return result + (result.endsWith('.') || result.endsWith('!') || result.endsWith('?') ? '' : '...');
    }
    if (wordCount < minWords) {
        console.log(`⚠️ Resposta muito curta (${wordCount} palavras), mantendo original`);
        return response;
    }
    return response;
}
// =========================================================
// CLASSE PRINCIPAL OTIMIZADA
// =========================================================
class GeminiAIService {
    constructor() {
        this.ai = null;
        this.model = null;
        this.isInitialized = false;
        const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY;
        if (!apiKey) {
            console.warn('⚠️  GEMINI_API_KEY não configurada. Serviço de IA desabilitado.');
            this.isInitialized = false;
            return;
        }
        try {
            this.ai = new generative_ai_1.GoogleGenerativeAI(apiKey);
            this.model = this.ai.getGenerativeModel({ model: "gemini-1.5-flash" });
            this.isInitialized = true;
            console.log('✅ Gemini AI inicializado com sucesso (modelo: gemini-1.5-flash)');
            // Iniciar limpeza apenas uma vez globalmente
            if (!GeminiAIService.cleanupStarted) {
                this.startAutoCleanup();
                GeminiAIService.cleanupStarted = true;
            }
        }
        catch (error) {
            console.error('❌ Erro ao inicializar Gemini AI:', error);
            this.isInitialized = false;
        }
    }
    startAutoCleanup() {
        // Limpeza a cada 6 horas (menos frequente)
        setInterval(async () => {
            await this.cleanupOldFiles();
        }, 6 * 60 * 60 * 1000);
        console.log('🧹 Limpeza automática de arquivos configurada (a cada 6 horas)');
    }
    async generateContent(promptParts) {
        var _a, _b;
        if (!this.isInitialized || !this.model) {
            throw new Error('Gemini AI não está inicializado');
        }
        try {
            console.log('🤖 Enviando requisição para Gemini...');
            if (Array.isArray(promptParts)) {
                console.log(`📋 Partes da requisição: ${promptParts.length}`);
                promptParts.forEach((part, index) => {
                    if (typeof part === 'string') {
                        console.log(`   ${index + 1}. Texto: ${part.substring(0, 100)}...`);
                    }
                    else if (part.inlineData) {
                        console.log(`   ${index + 1}. Mídia: ${part.inlineData.mimeType}, ${(part.inlineData.data.length / 1024).toFixed(1)}KB`);
                    }
                });
            }
            const result = await this.model.generateContent(promptParts);
            const response = await result.response;
            const text = response.text() || '';
            console.log('✅ Resposta recebida do Gemini');
            return text;
        }
        catch (error) {
            console.error('❌ Erro detalhado do Gemini:', {
                message: error.message,
                status: error.status,
                statusText: error.statusText,
                errorDetails: error.errorDetails
            });
            if (error.status === 400) {
                if ((_a = error.message) === null || _a === void 0 ? void 0 : _a.includes('invalid argument')) {
                    throw new Error('Formato de áudio não suportado ou dados inválidos');
                }
                else if ((_b = error.message) === null || _b === void 0 ? void 0 : _b.includes('quota')) {
                    throw new Error('Cota da API Gemini excedida');
                }
            }
            throw error;
        }
    }
    async getConversationContext(_contact, _limit = 10) {
        console.log('📝 Contexto de conversa não implementado para Supabase');
        return [];
    }
    // =========================================================
    // NOVA FUNÇÃO PRINCIPAL OTIMIZADA
    // =========================================================
    async generateOptimizedResponse(contact, message, audioData) {
        try {
            const startTime = Date.now();
            // 1. Classificação rápida da mensagem
            const messageType = AdaptiveResponseSystem.classifyMessage(message);
            const hasUrgency = AdaptiveResponseSystem.detectUrgency(message);
            console.log(`🔍 Tipo de mensagem: ${messageType}, Urgência: ${hasUrgency}`);
            // 2. Para mensagens simples SEM áudio, usa template (95% mais rápido)
            if (messageType !== 'complexa' && !audioData && !hasUrgency) {
                const response = AdaptiveResponseSystem.applyTemplate(contact, messageType);
                const processingTime = Date.now() - startTime;
                console.log(`⚡ Resposta por template em ${processingTime}ms`);
                return {
                    response: validateResponseLength(response, 30, 70),
                    sentiment: { type: 'positive', score: 0.8, emotions: ['cuidado', 'carinho'] },
                    needs: ['acompanhamento de rotina'],
                    suggestions: ['manter contato regular'],
                    priority: 'media',
                    medical_attention: false,
                    follow_up: ['acompanhar evolução'],
                    processingTime: `${processingTime}ms`,
                    method: 'template'
                };
            }
            // 3. Para mensagens complexas, urgentes ou com áudio, usa IA otimizada
            const context = await this.getConversationContext(contact, 3);
            const conversationSummary = PromptOptimizer.summarizeConversation(context);
            const result = await this.generateIntegratedResponse(contact, message, audioData, conversationSummary);
            const processingTime = Date.now() - startTime;
            console.log(`🤖 Resposta por IA em ${processingTime}ms`);
            return {
                ...result,
                processingTime: `${processingTime}ms`,
                method: 'ai_optimized'
            };
        }
        catch (error) {
            console.error('❌ Erro na resposta otimizada:', error);
            throw error;
        }
    }
    // =========================================================
    // ANÁLISE INTEGRADA - UMA ÚNICA CHAMADA
    // =========================================================
    async generateIntegratedResponse(contact, message, audioData, conversationSummary) {
        const hybridPrompt = PromptOptimizer.generateHybridPrompt(contact, message, !!audioData, conversationSummary);
        const promptParts = [hybridPrompt];
        if (audioData) {
            promptParts.push({
                inlineData: {
                    data: audioData.buffer.toString('base64'),
                    mimeType: this.validateMimeType(audioData.mimetype)
                }
            });
        }
        const rawResponse = await this.generateContent(promptParts);
        return this.parseHybridResponse(rawResponse);
    }
    parseHybridResponse(response) {
        try {
            // Extrai resposta da Rafaela
            const responseMatch = response.match(/RESPOSTA_RAFAELA:\s*(.*?)(?=ANÁLISE_JSON:|$)/s);
            const rafaelaResponse = responseMatch ? responseMatch[1].trim() : response;
            // Extrai e parseia análise JSON
            const analysisMatch = response.match(/ANÁLISE_JSON:\s*(\{.*?\})/s);
            let analysis = {
                sentiment: 'neutral',
                urgency: 'media',
                needs: ['acompanhamento geral'],
                medical: false
            };
            if (analysisMatch) {
                try {
                    analysis = JSON.parse(analysisMatch[1]);
                }
                catch (e) {
                    console.warn('⚠️ Fallback para análise padrão');
                }
            }
            return {
                response: validateResponseLength(rafaelaResponse, 30, 70),
                sentiment: {
                    type: analysis.sentiment,
                    score: analysis.sentiment === 'positive' ? 0.8 : analysis.sentiment === 'negative' ? 0.3 : 0.6,
                    emotions: analysis.sentiment === 'positive' ? ['alegria'] : analysis.sentiment === 'negative' ? ['preocupação'] : ['neutro']
                },
                needs: Array.isArray(analysis.needs) ? analysis.needs : ['acompanhamento'],
                suggestions: ['manter acompanhamento', 'observar sintomas'],
                priority: analysis.urgency || 'media',
                medical_attention: analysis.medical || false,
                follow_up: ['acompanhar evolução']
            };
        }
        catch (error) {
            console.error('❌ Erro no parse híbrido:', error);
            return {
                response: validateResponseLength(response, 30, 70),
                sentiment: { type: 'neutral', score: 0.5, emotions: ['indefinido'] },
                needs: ['revisar resposta'],
                suggestions: ['verificar logs'],
                priority: 'media',
                medical_attention: false,
                follow_up: ['acompanhar']
            };
        }
    }
    validateMimeType(mimetype) {
        const supportedTypes = ['audio/wav', 'audio/mp3', 'audio/ogg', 'audio/aac', 'audio/flac'];
        if (supportedTypes.includes(mimetype)) {
            return mimetype;
        }
        // Fallbacks para tipos comuns
        if (mimetype.includes('webm') || mimetype.includes('opus')) {
            return 'audio/ogg';
        }
        console.log(`🔄 MimeType convertido de ${mimetype} para audio/ogg`);
        return 'audio/ogg';
    }
    // =========================================================
    // FUNÇÃO ORIGINAL MANTIDA PARA COMPATIBILIDADE
    // =========================================================
    async generateResponse(contact, message, audioData) {
        console.log('⚠️ Usando método legado. Recomendado usar generateOptimizedResponse()');
        return this.generateOptimizedResponse(contact, message, audioData);
    }
    async generateFollowUpMessage(contact) {
        if (!this.isInitialized) {
            console.warn('⚠️ Gemini AI não inicializado, usando template padrão');
            const messageType = Math.random() > 0.5 ? 'saudacao' : 'movimento_bebe';
            const response = AdaptiveResponseSystem.applyTemplate(contact, messageType);
            return validateResponseLength(response, 20, 50);
        }
        try {
            console.log('🤖 Gerando mensagem de follow-up personalizada com IA...');
            const babyGender = contact.babyGender === 'male' ? 'bebezinho' :
                contact.babyGender === 'female' ? 'bebezinha' : 'bebê';
            const prompt = `Você é Rafaela, uma assistente virtual especializada em cuidados maternos.

Gere uma mensagem de acompanhamento carinhosa e personalizada para ${contact.name}.

Informações do contato:
- Nome: ${contact.name}
- Gênero do bebê: ${babyGender}

Diretrizes para a mensagem:
- Use um tom maternal, carinhoso e acolhedor
- Inclua o emoji 🧡 (marca da Rafaela)
- Use expressões como "nossa gente", "seguimos firmes", "minha fortaleza"
- Pergunte sobre o bem-estar da gestante e do bebê
- Seja proativa e demonstre interesse genuíno
- Mantenha entre 15-50 palavras
- Use linguagem natural e brasileira

Exemplos do estilo da Rafaela:
"Oi minha querida! Como você e o ${babyGender} estão hoje? Seguimos firmes nessa jornada! 🧡"
"Nossa gente, que saudade! Como anda tudo por aí? O ${babyGender} mexendo bastante? 🧡"

Gere uma mensagem única e personalizada:`;
            const result = await this.model.generateContent(prompt);
            const response = result.response.text().trim();
            console.log('✅ Mensagem de follow-up gerada com IA:', response.substring(0, 100) + '...');
            return validateResponseLength(response, 15, 50);
        }
        catch (error) {
            console.error('❌ Erro ao gerar follow-up com IA, usando template:', error);
            // Fallback para template
            const messageType = Math.random() > 0.5 ? 'saudacao' : 'movimento_bebe';
            const response = AdaptiveResponseSystem.applyTemplate(contact, messageType);
            return validateResponseLength(response, 20, 50);
        }
    }
    async cleanupOldFiles() {
        if (!this.ai)
            return;
        try {
            // Limpeza silenciosa - apenas log em caso de erro
            // A versão atual da biblioteca não suporta gerenciamento de arquivos
            // Para implementar, seria necessário atualizar para @google/generative-ai >= 0.12.0
        }
        catch (error) {
            console.error('❌ Erro na limpeza de arquivos:', error);
        }
    }
}
exports.GeminiAIService = GeminiAIService;
GeminiAIService.cleanupStarted = false;
