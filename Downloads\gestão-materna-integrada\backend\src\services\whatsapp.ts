import * as wppconnect from '@wppconnect-team/wppconnect';
import { Server as SocketServer } from 'socket.io';
import { Contact } from '../config/supabase';
import { IContact } from '../models/Contact';
import { contactService } from './contactService';
import { GeminiAIService } from './gemini';
import webhookService, { WhatsAppWebhookEvents } from './webhookService';
import healthMonitor from './healthMonitor';
import InterestEvaluatorService from './interestEvaluator';
// Audio handlers removidos (sistema simplificado)

// Função para converter Contact do Supabase para IContact do Gemini
function convertToIContact(contact: Contact): IContact {
  return {
    name: contact.name,
    phone: contact.phone,
    babyGender: contact.baby_gender || 'unknown',
    isActive: contact.is_active,
    lastInteraction: new Date(contact.last_interaction),
    registrationStatus: contact.registration_status as 'unregistered' | 'registered' | 'not_interested',
    evaluationMessages: contact.evaluation_messages,
    interestScore: contact.interest_score,
    evaluationStartDate: contact.evaluation_start_date ? new Date(contact.evaluation_start_date) : undefined,
    createdAt: new Date(contact.created_at || ''),
    updatedAt: new Date(contact.updated_at || ''),

    // Métodos vazios (não usados)
    updateInteraction: async () => {},
    incrementEvaluationMessages: async () => {},
    updateInterestScore: async () => {},
    markAsRegistered: async () => {},
    markAsNotInterested: async () => {},
    deactivate: async () => {},
    reactivate: async () => {}
  };
}

// Função para normalizar telefone do WhatsApp para formato brasileiro
function normalizePhoneToBrazilian(whatsappPhone: string): string {
  try {
    // Remover @c.us se existir
    let phone = whatsappPhone.replace('@c.us', '');

    // Se começar com 55 (código do Brasil), remover
    if (phone.startsWith('55')) {
      phone = phone.substring(2);
    }

    // Verificar se tem 11 dígitos (DDD + 9 + 8 dígitos)
    if (phone.length === 11) {
      const ddd = phone.substring(0, 2);
      const nono = phone.substring(2, 3);
      const primeiros4 = phone.substring(3, 7);
      const ultimos4 = phone.substring(7, 11);

      // Formato brasileiro: (DDD) 9XXXX-XXXX
      return `(${ddd}) ${nono}${primeiros4}-${ultimos4}`;
    }

    // Verificar se tem 10 dígitos mas deveria ter 11 (adicionar 9)
    if (phone.length === 10 && phone.substring(0, 2) !== '11') {
      const ddd = phone.substring(0, 2);
      const primeiros4 = phone.substring(2, 6);
      const ultimos4 = phone.substring(6, 10);

      // Adicionar 9 para celular: (DDD) 9XXXX-XXXX
      return `(${ddd}) 9${primeiros4}-${ultimos4}`;
    }

    // Se tem 10 dígitos (DDD + 8 dígitos - telefone fixo)
    if (phone.length === 10) {
      const ddd = phone.substring(0, 2);
      const primeiros4 = phone.substring(2, 6);
      const ultimos4 = phone.substring(6, 10);

      // Formato brasileiro: (DDD) XXXX-XXXX
      return `(${ddd}) ${primeiros4}-${ultimos4}`;
    }

    // Se não conseguir normalizar, retornar original
    console.warn('⚠️ Não foi possível normalizar telefone:', whatsappPhone);
    return whatsappPhone;

  } catch (error) {
    console.error('❌ Erro ao normalizar telefone:', error);
    return whatsappPhone;
  }
}

// Função para converter telefone brasileiro para formato WhatsApp
function phoneToWhatsAppFormat(brazilianPhone: string): string {
  try {
    // Remover caracteres especiais
    let phone = brazilianPhone.replace(/[^\d]/g, '');

    // Se não tem código do país, adicionar 55
    if (!phone.startsWith('55')) {
      phone = '55' + phone;
    }

    return phone + '@c.us';
  } catch (error) {
    console.error('❌ Erro ao converter para formato WhatsApp:', error);
    return brazilianPhone;
  }
}

export class WhatsAppClient {
  private client: any;
  private io: SocketServer;
  private geminiService: GeminiAIService;
  private interestEvaluator: InterestEvaluatorService;
  private isConnected: boolean = false;
  // Audio handler removido (sistema simplificado)

  constructor(io: SocketServer, geminiService: GeminiAIService) {
    this.io = io;
    this.geminiService = geminiService;
    this.interestEvaluator = new InterestEvaluatorService(geminiService);
    this.initialize();
  }

  private async initialize() {
    try {
      console.log('🔌 Iniciando WPPConnect...');

      this.client = await wppconnect.create({
        session: 'gestao-materna',
        catchQR: (base64Qr, asciiQR, attempts, urlCode) => {
          console.log('\n=== QR CODE PARA CONEXÃO DO WHATSAPP ===\n');

          // Verificar se asciiQR está disponível, senão usar uma mensagem alternativa
          if (asciiQR && asciiQR !== 'undefined') {
            console.log(asciiQR);
          } else {
            console.log('QR Code gerado - Escaneie com seu WhatsApp');
            console.log(`Tentativa: ${attempts || 1}`);
            if (urlCode) {
              console.log(`URL Code: ${urlCode}`);
            }
          }

          console.log('\n=======================================\n');

          // Enviar QR Code para o frontend se disponível
          if (base64Qr && base64Qr !== 'undefined') {
            this.io.emit('qr', base64Qr);
          }
        },
        statusFind: (statusSession: any, session: any) => {
          console.log('📱 Status da sessão:', statusSession, session);

          // Converter para string para evitar problemas de tipos
          const status = String(statusSession);

          if (status === 'qrReadSuccess') {
            console.log('✅ QR Code escaneado com sucesso!');
            this.io.emit('status', 'qr_scanned');
          } else if (status === 'isLogged') {
            this.isConnected = true;
            console.log('\n✅ Cliente WhatsApp conectado com sucesso!\n');
            this.io.emit('status', 'connected');
            // this.startFollowUpScheduler(); // Desabilitado temporariamente
          } else if (status === 'notLogged') {
            this.isConnected = false;
            console.log('❌ WhatsApp não está logado');
            this.io.emit('status', 'disconnected');
          } else if (status === 'autocloseCalled') {
            this.isConnected = false;
            console.log('⏰ Sessão fechada automaticamente');
            this.io.emit('status', 'timeout');
          } else if (status.includes('desconnected') || status.includes('disconnected')) {
            this.isConnected = false;
            console.log('📱 Dispositivo desconectado');
            this.io.emit('status', 'mobile_disconnected');
          } else if (status === 'browserClose') {
            this.isConnected = false;
            console.log('🌐 Browser fechado');
            this.io.emit('status', 'browser_closed');
          } else if (status === 'qrReadFail') {
            this.isConnected = false;
            console.log('❌ Falha ao ler QR Code');
            this.io.emit('status', 'qr_failed');
          } else if (status === 'inChat' || status === 'chatsAvailable') {
            this.isConnected = true;
            console.log('💬 Chats disponíveis - WhatsApp totalmente conectado!');
            this.io.emit('status', 'ready');

            // Inicializar handler automático de áudio
            this.initializeAutomaticAudioHandler();
          }
        },
        headless: true,
        devtools: false,
        useChrome: true,
        debug: false,
        logQR: true,
        browserArgs: ['--no-sandbox', '--disable-setuid-sandbox'],
        autoClose: 60000,
        puppeteerOptions: {
          userDataDir: './tokens/gestao-materna'
        }
      });

      // Configurar listener de mensagens
      this.client.onMessage(async (message: any) => {
        try {
          // Ignorar mensagens próprias
          if (message.fromMe) {
            console.log('📤 Mensagem própria ignorada');
            return;
          }

          // Ignorar mensagens criptografadas (sincronização inicial)
          if (message.type === 'ciphertext' || !message.body || message.body === 'undefined') {
            console.log('🔐 Mensagem criptografada/sincronização ignorada');
            return;
          }

          // Sistema de áudio removido (simplificado)
          if (message.type === 'ptt' || message.type === 'audio') {
            console.log('🎵 Áudio recebido mas processamento foi simplificado');
          }

          console.log('\n📱 ===== NOVA MENSAGEM RECEBIDA =====');
          console.log('📞 De:', message.from);
          console.log('💬 Conteúdo:', message.body);
          console.log('⏰ Timestamp:', new Date(message.timestamp * 1000).toLocaleString('pt-BR'));
          console.log('📋 Tipo:', message.type);
          console.log('👤 De mim:', message.fromMe);
          console.log('📱 Chat ID:', message.chatId);
          console.log('=====================================\n');

          const contact = await this.getOrCreateContact(message.from);
          console.log('👥 Contato processado:', {
            id: contact.id,
            name: contact.name,
            phone: contact.phone,
            registrationStatus: contact.registration_status
          });

          await this.saveMessage(message, contact);
          console.log('💾 Mensagem salva no MongoDB');

          let aiResponse: any;

          // Verificar status de registro do contato
          if (contact.registration_status === 'unregistered') {
            // Primeira mensagem de pessoa não cadastrada - analisar se é sobre o projeto
            console.log('🔍 Pessoa não cadastrada - analisando se é sobre projeto gestantes...');
            const analysis = await this.interestEvaluator.analyzeIfRelatedToProject(message.body);

            console.log(`📊 Análise: Relacionado=${analysis.isRelated}, Tipo=${analysis.messageType}, Saudação=${analysis.isGreeting}, Confiança=${analysis.confidence}%`);

            if (analysis.messageType === 'greeting') {
              // Saudação simples - resposta inteligente
              console.log('👋 Saudação detectada - gerando resposta inteligente');
              const greetingResponse = await this.interestEvaluator.generateGreetingResponse(message.body);

              await this.sendMessage(message.from, greetingResponse);
              console.log('✅ Resposta de saudação enviada');

              aiResponse = { response: greetingResponse, sentiment: null, needs: ['Saudação respondida'], suggestions: [] };

            } else if (analysis.isRelated && analysis.shouldOfferRegistration) {
              // Mensagem relacionada ao projeto - oferecer cadastro
              console.log('✅ Mensagem relacionada ao projeto - oferecendo cadastro');
              const registrationOffer = await this.interestEvaluator.generateRegistrationOffer(message.body);

              // Marcar como registrada (simplificado)
              await contactService.update(contact.id!, { registration_status: 'registered' });

              await this.sendMessage(message.from, registrationOffer);
              console.log('✅ Oferta de cadastro enviada');

              aiResponse = { response: registrationOffer, sentiment: null, needs: ['Cadastro oferecido'], suggestions: [] };

            } else if (analysis.isRelated && !analysis.shouldOfferRegistration) {
              // Relacionado mas precisa de mais contexto - fazer pergunta
              console.log('🔍 Relacionado mas precisa de mais contexto - continuando avaliação');

              // Fazer pergunta para entender melhor
              const clarificationResponse = 'Entendi. Você está grávida ou tem alguma dúvida sobre gravidez?';
              await this.sendMessage(message.from, clarificationResponse);
              console.log('✅ Pergunta de esclarecimento enviada');

              aiResponse = { response: clarificationResponse, sentiment: null, needs: ['Esclarecimento'], suggestions: [] };

            } else if (analysis.messageType === 'unrelated') {
              // Não relacionado ao projeto - marcar como não interessada
              console.log('🚫 Mensagem não relacionada ao projeto - não respondendo (deixando para humano)');
              await contactService.update(contact.id!, {
                registration_status: 'not_interested',
                is_active: false
              });

              // Não enviar resposta - deixar para humano
              return;
            } else {
              // Mensagem unclear - fazer pergunta genérica
              console.log('❓ Mensagem não clara - fazendo pergunta de esclarecimento');
              const clarificationResponse = 'Entendi. Você está grávida ou tem alguma dúvida sobre gestação? Estou aqui para ajudar! 🧡';
              await this.sendMessage(message.from, clarificationResponse);
              console.log('✅ Pergunta de esclarecimento enviada');

              aiResponse = { response: clarificationResponse, sentiment: null, needs: ['Esclarecimento'], suggestions: [] };
            }

          } else if (contact.registration_status === 'registered') {
            // Pessoa registrada - processamento normal com IA
            console.log('🤖 Processamento normal com IA para pessoa registrada...');
            const iContact = convertToIContact(contact);
            aiResponse = await this.geminiService.generateOptimizedResponse(iContact, message.body);
            console.log('🧠 Resposta da IA gerada:', {
              response: aiResponse.response.substring(0, 100) + '...',
              sentiment: aiResponse.sentiment,
              needs: aiResponse.needs,
              suggestions: aiResponse.suggestions
            });

            await this.sendMessage(message.from, aiResponse.response);
            console.log('✅ Resposta da IA enviada');

            // Atualizar última interação
            await contactService.updateLastInteraction(contact.id!);
            console.log('📊 Contato atualizado com análise da IA');

          } else {
            // Pessoa não interessada - não responder
            console.log('🚫 Pessoa marcada como não interessada - ignorando mensagem');
            return;
          }

          // Emitir evento para frontend
          const eventData = {
            from: message.from,
            body: message.body,
            timestamp: message.timestamp,
            analysis: aiResponse,
            contact: {
              id: contact.id,
              name: contact.name,
              phone: contact.phone
            }
          };

          this.io.emit('message', eventData);
          this.io.emit('newMessage', eventData);
          console.log('📡 Evento emitido para frontend');
          console.log('\n🎉 ===== PROCESSAMENTO CONCLUÍDO =====\n');

        } catch (error: any) {
          console.error('\n❌ ===== ERRO NO PROCESSAMENTO =====');
          console.error('📱 Mensagem:', message.body);
          console.error('📞 De:', message.from);
          console.error('🚨 Erro:', error);
          console.error('=====================================\n');

          await this.sendMessage(message.from, 'Desculpe, tive um problema ao processar sua mensagem. Por favor, tente novamente em alguns instantes.');
        }
      });

      // Configurar webhooks para eventos importantes
      this.setupWebhookEvents();

    } catch (error) {
      console.error('❌ Erro ao inicializar WPPConnect:', error);
      this.io.emit('status', 'error');

      // Emitir evento de erro via webhook
      await webhookService.emitWebhookEvent({
        type: WhatsAppWebhookEvents.ERROR,
        data: {
          error: (error as any).message,
          context: 'initialization'
        },
        source: 'whatsapp'
      });
    }
  }

  // Configurar eventos de webhook
  private setupWebhookEvents() {
    console.log('📡 Configurando webhooks para eventos do WhatsApp...');

    // Eventos de mudança de estado
    if (this.client.onStateChange) {
      this.client.onStateChange(async (state: string) => {
        console.log('📡 Estado do WhatsApp alterado:', state);

        await webhookService.emitWebhookEvent({
          type: WhatsAppWebhookEvents.CONNECTION_STATUS,
          data: {
            state,
            timestamp: new Date().toISOString(),
            isConnected: this.isConnected
          },
          source: 'whatsapp'
        });
      });
    }

    // Eventos de QR Code
    if (this.client.catchQR) {
      this.client.catchQR(async (qrCode: string) => {
        console.log('📡 QR Code atualizado');

        await webhookService.emitWebhookEvent({
          type: WhatsAppWebhookEvents.QR_UPDATED,
          data: {
            qrCode,
            timestamp: new Date().toISOString()
          },
          source: 'whatsapp'
        });
      });
    }

    console.log('✅ Webhooks configurados com sucesso');
  }

  // NOVO: Inicializar handler automático de áudio
  private initializeAutomaticAudioHandler(): void {
    if (!this.client) {
      console.warn('⚠️ Cliente WhatsApp não disponível para handler de áudio');
      return;
    }

    try {
      // Audio handler removido (sistema simplificado)

      // Sistema de áudio removido (simplificado)
      console.log('🎵 Handler automático de áudio inicializado e ativado (simplificado)');

    } catch (error) {
      console.error('❌ Erro ao inicializar handler automático de áudio:', error);
    }
  }

  private async getOrCreateContact(whatsappPhone: string): Promise<Contact> {
    // Normalizar telefone para formato brasileiro
    const brazilianPhone = normalizePhoneToBrazilian(whatsappPhone);
    console.log('📞 Telefone normalizado:', whatsappPhone, '→', brazilianPhone);

    // Buscar contato pelo telefone brasileiro
    let contact = await contactService.findByPhone(brazilianPhone);

    if (!contact) {
      try {
        // Tentar obter informações do contato
        const info = await this.client.getContact(whatsappPhone);

        // Criar contato em estado não registrado
        contact = await contactService.create({
          phone: brazilianPhone,
          name: info.name || info.pushname || `Usuário ${brazilianPhone.slice(-4)}`,
          last_interaction: new Date().toISOString(),
          is_active: true,
          registration_status: 'unregistered',
          evaluation_messages: 0,
          interest_score: 0
        });
        console.log('👤 Novo contato não registrado criado:', contact.name, 'Tel:', brazilianPhone);
      } catch (error) {
        // Se não conseguir obter info, criar com dados básicos
        contact = await contactService.create({
          phone: brazilianPhone,
          name: `Usuário ${brazilianPhone.slice(-4)}`,
          last_interaction: new Date().toISOString(),
          is_active: true,
          registration_status: 'unregistered',
          evaluation_messages: 0,
          interest_score: 0
        });
        console.log('👤 Novo contato não registrado criado (básico):', contact.name, 'Tel:', brazilianPhone);
      }
    } else {
      // Atualizar última interação
      await contactService.updateLastInteraction(contact.id!);
      console.log('👤 Contato existente encontrado:', contact.name, 'Tel:', brazilianPhone, 'Status:', contact.registration_status);
    }
    return contact;
  }

  private async saveMessage(message: any, contact: Contact) {
    // Salvar mensagem no Supabase seria implementado aqui
    // Por enquanto, apenas log
    console.log('💾 Mensagem salva:', {
      contact_id: contact.id,
      content: message.body,
      timestamp: message.timestamp,
      type: message.type,
      from_me: message.fromMe
    });
  }

  // Método removido - análise agora é feita via updateLastInteraction

  public async sendMessage(to: string, message: string) {
    try {
      if (!this.isConnected) {
        throw new Error('WhatsApp não está conectado');
      }

      // Converter telefone brasileiro para formato WhatsApp se necessário
      let whatsappPhone = to;
      if (!to.includes('@c.us')) {
        whatsappPhone = phoneToWhatsAppFormat(to);
        console.log('📞 Telefone convertido para WhatsApp:', to, '→', whatsappPhone);
      }

      console.log('📤 Enviando mensagem para:', whatsappPhone);
      const response = await this.client.sendText(whatsappPhone, message);

      // Salvar mensagem enviada (usar telefone WhatsApp para buscar contato)
      const contact = await this.getOrCreateContact(whatsappPhone);
      await this.saveMessage({
        body: message,
        timestamp: Date.now(),
        type: 'chat',
        fromMe: true
      }, contact);

      console.log('✅ Mensagem enviada e salva');

      // Emitir evento de webhook para mensagem enviada
      await webhookService.emitWebhookEvent({
        type: WhatsAppWebhookEvents.MESSAGE_SENT,
        data: {
          to: whatsappPhone,
          message,
          timestamp: new Date().toISOString(),
          contact: {
            id: contact.id,
            name: contact.name,
            phone: contact.phone
          }
        },
        source: 'whatsapp'
      });

      return response;
    } catch (error) {
      console.error('❌ Erro ao enviar mensagem:', error);
      throw error;
    }
  }

  public async sendBulkMessages(contacts: string[], message: string) {
    const results = await Promise.allSettled(
      contacts.map(contact => this.sendMessage(contact, message))
    );
    return results;
  }

  // Método para obter status da conexão (baseado na documentação oficial)
  public async getStatus() {
    try {
      if (!this.client) {
        return {
          connected: false,
          status: 'not_initialized',
          qr: null,
          info: null
        };
      }

      // Usar métodos oficiais da documentação
      const isConnected = await this.client.isConnected();
      const isAuthenticated = await this.client.isAuthenticated();
      const connectionState = await this.client.getConnectionState();

      let qrCode = null;
      try {
        // Tentar obter QR code se não estiver conectado
        if (!isConnected && !isAuthenticated) {
          qrCode = await this.client.getQrCode();
        }
      } catch (qrError: any) {
        // QR code pode não estar disponível
        console.log('QR Code não disponível:', qrError.message);
      }

      let userInfo = null;
      if (isConnected && isAuthenticated) {
        try {
          userInfo = await this.client.getHostDevice();
        } catch (infoError: any) {
          console.log('Info do usuário não disponível:', infoError.message);
        }
      }

      return {
        connected: isConnected,
        authenticated: isAuthenticated,
        status: isConnected ? 'connected' : 'disconnected',
        connectionState,
        qr: qrCode,
        info: userInfo
      };
    } catch (error: any) {
      console.error('❌ Erro ao obter status:', error);
      return {
        connected: false,
        authenticated: false,
        status: 'error',
        connectionState: 'UNPAIRED',
        qr: null,
        info: null,
        error: error.message
      };
    }
  }

  private async startFollowUpScheduler() {
    // Verificar contatos a cada 24 horas, mas enviar follow-up apenas após 15 dias sem contato
    setInterval(async () => {
      try {
        console.log('🔍 Verificando contatos para follow-up (15 dias sem contato)...');

        const fifteenDaysAgo = new Date(Date.now() - 15 * 24 * 60 * 60 * 1000);
        const contacts = await contactService.findAll({
          isActive: true,
          limit: 100
        });

        // Filtrar contatos sem interação há 15+ dias
        const contactsForFollowUp = contacts.filter(contact => {
          const lastInteraction = new Date(contact.last_interaction);
          return lastInteraction < fifteenDaysAgo;
        });

        console.log(`📋 Encontrados ${contactsForFollowUp.length} contatos sem interação há 15+ dias`);

        for (const contact of contactsForFollowUp) {
          try {
            const lastInteractionDate = new Date(contact.last_interaction).toLocaleDateString('pt-BR');
            console.log(`📤 Enviando follow-up para ${contact.name} (última interação: ${lastInteractionDate})`);

            const iContact = convertToIContact(contact);
            const followUpMessage = await this.geminiService.generateFollowUpMessage(iContact);
            await this.sendMessage(contact.phone, followUpMessage);

            // Atualizar última interação para evitar spam
            await contactService.updateLastInteraction(contact.id!);

            console.log(`✅ Follow-up enviado para ${contact.name}`);

            // Delay entre mensagens para não sobrecarregar
            await new Promise(resolve => setTimeout(resolve, 3000));
          } catch (error) {
            console.error(`❌ Erro ao enviar follow-up para ${contact.name}:`, error);
          }
        }

        console.log('✅ Verificação de follow-up concluída');
      } catch (error) {
        console.error('❌ Erro ao verificar follow-ups:', error);
      }
    }, 24 * 60 * 60 * 1000); // Verifica a cada 24 horas
  }

  public getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      client: !!this.client,
      automaticAudio: null // Removido
    };
  }

  // NOVO: Obter estatísticas completas do sistema
  public getSystemStats() {
    return {
      connection: this.getConnectionStatus(),
      audioQueue: null, // Removido
      automaticHandler: null // Removido
    };
  }

  // Sistema de áudio removido (simplificado)
  public toggleAutomaticAudio(enable: boolean): boolean {
    console.log(`🎵 Processamento automático de áudio ${enable ? 'ativado' : 'desativado'} (simplificado)`);
    return true;
  }
}
