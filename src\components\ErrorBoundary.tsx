import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON>riangle, RefreshCw, Home, Bug } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

/**
 * Error Boundary para capturar e tratar erros React
 * Fornece interface amigável e logging detalhado
 */
export class ErrorBoundary extends Component<Props, State> {
  private retryCount = 0;
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Gerar ID único para o erro
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      hasError: true,
      error,
      errorId
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Atualizar estado com informações detalhadas
    this.setState({
      error,
      errorInfo
    });

    // Log detalhado do erro
    this.logError(error, errorInfo);

    // Callback customizado se fornecido
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Enviar erro para serviço de monitoramento
    this.reportError(error, errorInfo);
  }

  /**
   * Log detalhado do erro
   */
  private logError(error: Error, errorInfo: ErrorInfo) {
    const errorDetails = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: localStorage.getItem('user_id') || 'anonymous',
      retryCount: this.retryCount
    };

    console.group('🚨 Error Boundary - Erro Capturado');
    console.error('Error:', error);
    console.error('Error Info:', errorInfo);
    console.table(errorDetails);
    console.groupEnd();

    // Salvar no localStorage para debug
    try {
      const savedErrors = JSON.parse(localStorage.getItem('app_errors') || '[]');
      savedErrors.push(errorDetails);
      
      // Manter apenas os últimos 10 erros
      if (savedErrors.length > 10) {
        savedErrors.shift();
      }
      
      localStorage.setItem('app_errors', JSON.stringify(savedErrors));
    } catch (storageError) {
      console.warn('Erro ao salvar no localStorage:', storageError);
    }
  }

  /**
   * Reportar erro para serviço de monitoramento
   */
  private async reportError(error: Error, errorInfo: ErrorInfo) {
    try {
      // Em produção, enviar para serviço de monitoramento
      const errorReport = {
        error: {
          message: error.message,
          stack: error.stack,
          name: error.name
        },
        errorInfo: {
          componentStack: errorInfo.componentStack
        },
        context: {
          errorId: this.state.errorId,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          url: window.location.href,
          userId: localStorage.getItem('user_id') || 'anonymous'
        }
      };

      // Simular envio para API de monitoramento
      console.log('📊 Enviando erro para monitoramento:', errorReport);
      
      // Em produção, descomentar:
      /*
      await fetch('/api/errors/report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(errorReport)
      });
      */

    } catch (reportError) {
      console.error('Erro ao reportar erro:', reportError);
    }
  }

  /**
   * Tentar recuperar do erro
   */
  private handleRetry = () => {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      console.log(`🔄 Tentativa de recuperação ${this.retryCount}/${this.maxRetries}`);
      
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: null
      });
    } else {
      console.warn('⚠️ Máximo de tentativas atingido');
    }
  };

  /**
   * Recarregar página
   */
  private handleReload = () => {
    window.location.reload();
  };

  /**
   * Ir para página inicial
   */
  private handleGoHome = () => {
    window.location.href = '/';
  };

  /**
   * Copiar detalhes do erro
   */
  private handleCopyError = () => {
    const errorDetails = {
      errorId: this.state.errorId,
      message: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
      timestamp: new Date().toISOString(),
      url: window.location.href
    };

    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
      .then(() => {
        alert('Detalhes do erro copiados para a área de transferência');
      })
      .catch(() => {
        console.error('Erro ao copiar para área de transferência');
      });
  };

  render() {
    if (this.state.hasError) {
      // Usar fallback customizado se fornecido
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Interface padrão de erro
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
            {/* Ícone e título */}
            <div className="text-center mb-6">
              <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                <AlertTriangle className="w-8 h-8 text-red-600" />
              </div>
              <h1 className="text-xl font-semibold text-gray-900 mb-2">
                Oops! Algo deu errado
              </h1>
              <p className="text-gray-600 text-sm">
                Encontramos um erro inesperado. Nossa equipe foi notificada.
              </p>
            </div>

            {/* ID do erro */}
            {this.state.errorId && (
              <div className="bg-gray-50 rounded-lg p-3 mb-4">
                <p className="text-xs text-gray-500 mb-1">ID do Erro:</p>
                <p className="text-sm font-mono text-gray-700 break-all">
                  {this.state.errorId}
                </p>
              </div>
            )}

            {/* Detalhes do erro (se habilitado) */}
            {this.props.showDetails && this.state.error && (
              <details className="mb-4">
                <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
                  Ver detalhes técnicos
                </summary>
                <div className="mt-2 p-3 bg-gray-50 rounded text-xs font-mono text-gray-700 max-h-32 overflow-y-auto">
                  <p><strong>Erro:</strong> {this.state.error.message}</p>
                  {this.state.error.stack && (
                    <pre className="mt-2 whitespace-pre-wrap">
                      {this.state.error.stack}
                    </pre>
                  )}
                </div>
              </details>
            )}

            {/* Ações */}
            <div className="space-y-3">
              {/* Tentar novamente */}
              {this.retryCount < this.maxRetries && (
                <button
                  onClick={this.handleRetry}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  Tentar Novamente ({this.maxRetries - this.retryCount} restantes)
                </button>
              )}

              {/* Recarregar página */}
              <button
                onClick={this.handleReload}
                className="w-full bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors flex items-center justify-center gap-2"
              >
                <RefreshCw className="w-4 h-4" />
                Recarregar Página
              </button>

              {/* Ir para início */}
              <button
                onClick={this.handleGoHome}
                className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center gap-2"
              >
                <Home className="w-4 h-4" />
                Ir para Início
              </button>

              {/* Copiar erro */}
              {this.props.showDetails && (
                <button
                  onClick={this.handleCopyError}
                  className="w-full bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700 transition-colors flex items-center justify-center gap-2"
                >
                  <Bug className="w-4 h-4" />
                  Copiar Detalhes do Erro
                </button>
              )}
            </div>

            {/* Informações de contato */}
            <div className="mt-6 pt-4 border-t border-gray-200 text-center">
              <p className="text-xs text-gray-500">
                Se o problema persistir, entre em contato com o suporte
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook para capturar erros em componentes funcionais
 */
export const useErrorHandler = () => {
  const handleError = (error: Error, errorInfo?: any) => {
    console.error('🚨 Erro capturado pelo hook:', error);
    
    // Log do erro
    const errorDetails = {
      message: error.message,
      stack: error.stack,
      errorInfo,
      timestamp: new Date().toISOString(),
      url: window.location.href
    };

    console.table(errorDetails);

    // Salvar no localStorage
    try {
      const savedErrors = JSON.parse(localStorage.getItem('app_errors') || '[]');
      savedErrors.push(errorDetails);
      localStorage.setItem('app_errors', JSON.stringify(savedErrors));
    } catch (storageError) {
      console.warn('Erro ao salvar no localStorage:', storageError);
    }
  };

  return { handleError };
};

/**
 * Componente de erro simples para casos específicos
 */
export const ErrorFallback: React.FC<{
  error?: Error;
  resetError?: () => void;
  message?: string;
}> = ({ error, resetError, message }) => (
  <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
    <div className="flex items-center gap-2 text-red-800 mb-2">
      <AlertTriangle className="w-5 h-5" />
      <h3 className="font-medium">Erro</h3>
    </div>
    <p className="text-red-700 text-sm mb-3">
      {message || error?.message || 'Ocorreu um erro inesperado'}
    </p>
    {resetError && (
      <button
        onClick={resetError}
        className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors"
      >
        Tentar Novamente
      </button>
    )}
  </div>
);
