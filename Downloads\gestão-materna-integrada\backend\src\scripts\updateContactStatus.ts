import { supabase } from '../config/supabase';

/**
 * Script para atualizar o status de registro de um contato
 */

async function updateContactStatus(contactId: string, status: 'registered' | 'unregistered' | 'not_interested') {
  console.log(`🔄 Atualizando status do contato ${contactId} para '${status}'...`);
  
  try {
    const { data, error } = await supabase
      .from('contacts')
      .update({ 
        registration_status: status,
        last_interaction: new Date().toISOString()
      })
      .eq('id', contactId)
      .select()
      .single();

    if (error) {
      console.error('❌ Erro ao atualizar contato:', error);
      throw error;
    }

    console.log('✅ Contato atualizado com sucesso:', {
      id: data.id,
      name: data.name,
      phone: data.phone,
      registration_status: data.registration_status
    });

    return data;

  } catch (error) {
    console.error('❌ Erro durante a atualização:', error);
    throw error;
  }
}

// Executar o script se chamado diretamente
if (require.main === module) {
  const contactId = process.argv[2];
  const status = process.argv[3] as 'registered' | 'unregistered' | 'not_interested';
  
  if (!contactId || !status) {
    console.error('❌ Uso: npx ts-node updateContactStatus.ts <contactId> <status>');
    console.error('   Status válidos: registered, unregistered, not_interested');
    process.exit(1);
  }

  updateContactStatus(contactId, status)
    .then(() => {
      console.log('🏁 Script finalizado com sucesso');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Falha na atualização:', error);
      process.exit(1);
    });
}

export { updateContactStatus };
