import { wppConnectIntegration } from './wppConnectIntegration';


/**
 * Serviço para inicialização automática do WhatsApp
 * Gerencia conexão, reconexão e monitoramento
 */
export class WhatsAppAutoStartService {
  private isRunning: boolean = false;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private startupAttempts: number = 0;
  private maxStartupAttempts: number = 3;

  /**
   * Iniciar serviço automático
   */
  public async start(): Promise<boolean> {
    if (this.isRunning) {
      console.log('⚠️ Serviço WhatsApp já está rodando');
      return true;
    }

    console.log('🚀 Iniciando serviço automático do WhatsApp...');
    this.isRunning = true;

    try {
      // Tentar inicializar wppConnect
      const success = await this.initializeWithRetry();
      
      if (success) {
        // Iniciar monitoramento de saúde
        this.startHealthCheck();
        
        // Configurar listeners de eventos
        this.setupEventListeners();
        
        console.log('✅ Serviço WhatsApp iniciado com sucesso!');
        return true;
      } else {
        console.error('❌ Falha ao inicializar WhatsApp após múltiplas tentativas');
        this.isRunning = false;
        return false;
      }

    } catch (error) {
      console.error('❌ Erro crítico ao iniciar serviço WhatsApp:', error);
      this.isRunning = false;
      return false;
    }
  }

  /**
   * Inicializar com retry
   */
  private async initializeWithRetry(): Promise<boolean> {
    for (let attempt = 1; attempt <= this.maxStartupAttempts; attempt++) {
      try {
        console.log(`🔄 Tentativa ${attempt}/${this.maxStartupAttempts} de inicialização...`);
        
        const success = await wppConnectIntegration.initialize();
        
        if (success) {
          console.log(`✅ Inicialização bem-sucedida na tentativa ${attempt}`);
          this.startupAttempts = 0;
          return true;
        }

        // Aguardar antes da próxima tentativa
        if (attempt < this.maxStartupAttempts) {
          const delay = attempt * 5000; // 5s, 10s, 15s
          console.log(`⏳ Aguardando ${delay}ms antes da próxima tentativa...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }

      } catch (error) {
        console.error(`❌ Erro na tentativa ${attempt}:`, error);
      }
    }

    return false;
  }

  /**
   * Configurar listeners de eventos
   */
  private setupEventListeners(): void {
    console.log('🎧 Configurando listeners do serviço automático...');
    // Sistema de áudio removido (simplificado)
    console.log('✅ Listeners configurados (sistema simplificado)');
  }

  /**
   * Iniciar verificação de saúde
   */
  private startHealthCheck(): void {
    console.log('🏥 Iniciando monitoramento de saúde...');

    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, 30000); // Verificar a cada 30 segundos

    console.log('✅ Monitoramento de saúde ativo');
  }

  /**
   * Realizar verificação de saúde
   */
  private async performHealthCheck(): Promise<void> {
    try {
      const status = wppConnectIntegration.getConnectionStatus();
      const stats = { processing: { processed: 0, failed: 0, avgProcessingTime: 0 } }; // Simplificado

      // Verificar conexão WhatsApp
      if (!status.isConnected || !status.isAuthenticated) {
        console.warn('⚠️ WhatsApp desconectado, tentando reconectar...');
        await this.handleDisconnection();
        return;
      }

      // Verificar saúde da fila de áudio
      const successRate = 100; // Simplificado
      if (successRate < 80 && stats.processing.processed > 10) {
        console.warn(`⚠️ Taxa de sucesso baixa: ${successRate.toFixed(1)}%`);
      }

      // Verificar atividade recente
      if (status.lastActivity) {
        const timeSinceActivity = Date.now() - status.lastActivity.getTime();
        const hoursInactive = timeSinceActivity / (1000 * 60 * 60);
        
        if (hoursInactive > 2) {
          console.log(`📊 Sem atividade há ${hoursInactive.toFixed(1)} horas`);
        }
      }

      // Log de status periódico (a cada 5 minutos)
      const now = new Date();
      if (now.getMinutes() % 5 === 0 && now.getSeconds() < 30) {
        this.logSystemStatus();
      }

    } catch (error) {
      console.error('❌ Erro na verificação de saúde:', error);
    }
  }

  /**
   * Lidar com desconexão
   */
  private async handleDisconnection(): Promise<void> {
    try {
      console.log('🔄 Tentando reconectar WhatsApp...');
      
      // Tentar reconectar
      const success = await wppConnectIntegration.initialize();
      
      if (success) {
        console.log('✅ Reconexão bem-sucedida!');
      } else {
        console.error('❌ Falha na reconexão');
      }

    } catch (error) {
      console.error('❌ Erro ao tentar reconectar:', error);
    }
  }

  /**
   * Log de status do sistema
   */
  private logSystemStatus(): void {
    const connectionStatus = wppConnectIntegration.getConnectionStatus();
    const queueStats = { processing: { processed: 0, failed: 0, avgProcessingTime: 0 } }; // Simplificado

    console.log('\n📊 ===== STATUS DO SISTEMA =====');
    console.log(`📱 WhatsApp: ${connectionStatus.isConnected ? '✅ Conectado' : '❌ Desconectado'}`);
    console.log(`🔐 Autenticado: ${connectionStatus.isAuthenticated ? '✅ Sim' : '❌ Não'}`);
    console.log(`📞 Telefone: ${connectionStatus.phoneNumber || 'N/A'}`);
    console.log(`📋 Fila: 0 pendentes, 0 processando (sistema simplificado)`);
    console.log(`📈 Taxa de sucesso: 100.0% (sistema simplificado)`);
    console.log(`⏱️ Tempo médio: ${queueStats.processing.avgProcessingTime.toFixed(0)}ms`);
    console.log(`🎵 Total processado: ${queueStats.processing.processed}`);
    console.log('===============================\n');
  }

  /**
   * Parar serviço
   */
  public async stop(): Promise<void> {
    console.log('🛑 Parando serviço automático do WhatsApp...');

    this.isRunning = false;

    // Parar monitoramento
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    // Desconectar WhatsApp
    await wppConnectIntegration.disconnect();

    // Fila de áudio removida

    console.log('✅ Serviço WhatsApp parado');
  }

  /**
   * Reiniciar serviço
   */
  public async restart(): Promise<boolean> {
    console.log('🔄 Reiniciando serviço WhatsApp...');
    
    await this.stop();
    await new Promise(resolve => setTimeout(resolve, 2000)); // Aguardar 2s
    
    return await this.start();
  }

  /**
   * Obter status completo
   */
  public getStatus() {
    return {
      isRunning: this.isRunning,
      startupAttempts: this.startupAttempts,
      connection: wppConnectIntegration.getConnectionStatus(),
      stats: wppConnectIntegration.getStats(),
      uptime: this.isRunning ? process.uptime() : 0
    };
  }

  /**
   * Enviar mensagem de teste
   */
  public async sendTestMessage(phoneNumber: string): Promise<boolean> {
    try {
      const testMessage = `🧡 Teste do sistema Rafaela Cuida
      
✅ WhatsApp conectado
🎵 Processamento de áudio ativo
🤖 IA Gemini funcionando

Envie um áudio para testar!

Horário: ${new Date().toLocaleString('pt-BR')}`;

      const success = await wppConnectIntegration.sendText(phoneNumber, testMessage);
      
      if (success) {
        console.log(`✅ Mensagem de teste enviada para ${phoneNumber}`);
      } else {
        console.error(`❌ Falha ao enviar mensagem de teste para ${phoneNumber}`);
      }

      return success;

    } catch (error) {
      console.error('❌ Erro ao enviar mensagem de teste:', error);
      return false;
    }
  }

  /**
   * Forçar processamento de áudio de teste
   */
  public async testAudioProcessing(): Promise<string> {
    try {
      console.log('🧪 Iniciando teste de processamento de áudio...');

      // Sistema de áudio removido
      console.log('📋 Sistema de áudio não disponível (removido)');
      return 'audio_system_removed';

    } catch (error) {
      console.error('❌ Erro no teste de processamento:', error);
      throw error;
    }
  }
}

// Instância singleton
export const whatsappAutoStart = new WhatsAppAutoStartService();
