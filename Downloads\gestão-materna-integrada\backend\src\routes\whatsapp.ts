import express, { Request, Response } from 'express';
import { body, validationResult } from 'express-validator';
import { WhatsAppClient } from '../services/whatsapp';
import { contactService } from '../services/contactService';
import { authenticate, authorize, auditLog } from '../middleware/auth';

const router = express.Router();


let whatsappClient: WhatsAppClient;

export function setWhatsAppClient(client: WhatsAppClient) {
  whatsappClient = client;
}


router.get('/status',
  authenticate,
  async (req: Request, res: Response) => {
    try {
      if (!whatsappClient) {
        return res.status(503).json({
          error: 'WhatsApp client não inicializado',
          code: 'CLIENT_NOT_INITIALIZED'
        });
      }

      const status = await whatsappClient.getStatus();

      res.json({
        connected: status.connected,
        authenticated: status.authenticated,
        status: status.status,
        connectionState: status.connectionState,
        qr: status.qr,
        info: status.info,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Erro ao obter status WhatsApp:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);


router.get('/qr',
  authenticate,
  authorize('manage:system'),
  async (req: Request, res: Response) => {
    try {
      if (!whatsappClient) {
        return res.status(503).json({
          error: 'WhatsApp client não inicializado',
          code: 'CLIENT_NOT_INITIALIZED'
        });
      }

      const status = await whatsappClient.getStatus();

      if (status.connected) {
        return res.json({
          message: 'WhatsApp já está conectado',
          connected: true
        });
      }


      res.json({
        message: 'QR Code será exibido no console do servidor quando necessário',
        connected: false,
        instructions: 'Verifique o console do servidor para escanear o QR Code'
      });
    } catch (error) {
      console.error('Erro ao obter QR Code:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);


router.post('/send',
  authenticate,
  authorize('write:messages'),
  [
    body('phone')
      .matches(/^\+?[\d\s\-\(\)]+$/)
      .withMessage('Formato de telefone inválido'),
    body('message')
      .trim()
      .isLength({ min: 1, max: 4000 })
      .withMessage('Mensagem deve ter entre 1 e 4000 caracteres'),
    body('contactId')
      .optional()
      .isMongoId()
      .withMessage('ID do contato inválido')
  ],
  auditLog('SEND_WHATSAPP_MESSAGE'),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      const status = await whatsappClient?.getStatus();
      if (!whatsappClient || !status?.connected) {
        return res.status(503).json({
          error: 'WhatsApp não está conectado',
          code: 'WHATSAPP_NOT_CONNECTED'
        });
      }

      const { phone, message, contactId } = req.body;

      // Verificar se contato existe (se fornecido)
      let contact = null;
      if (contactId) {
        contact = await contactService.findById(contactId);
        if (!contact) {
          return res.status(404).json({
            error: 'Contato não encontrado',
            code: 'CONTACT_NOT_FOUND'
          });
        }
      } else {
        // Buscar contato pelo telefone
        contact = await contactService.findByPhone(phone.replace(/\D/g, ''));
      }

      // Enviar mensagem
      const success = await whatsappClient.sendMessage(phone, message);

      if (!success) {
        return res.status(500).json({
          error: 'Falha ao enviar mensagem',
          code: 'SEND_FAILED'
        });
      }

      res.json({
        message: 'Mensagem enviada com sucesso',
        phone,
        content: message,
        timestamp: new Date().toISOString(),
        contact: contact ? {
          id: contact.id,
          name: contact.name,
          phone: contact.phone
        } : null
      });
    } catch (error) {
      console.error('Erro ao enviar mensagem WhatsApp:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// POST /api/whatsapp/send-bulk - Enviar mensagem em massa
router.post('/send-bulk',
  authenticate,
  authorize('send:bulk_messages'),
  [
    body('contacts')
      .isArray({ min: 1, max: 100 })
      .withMessage('Lista de contatos deve ter entre 1 e 100 itens'),
    body('contacts.*.phone')
      .matches(/^\+?[\d\s\-\(\)]+$/)
      .withMessage('Formato de telefone inválido'),
    body('message')
      .trim()
      .isLength({ min: 1, max: 4000 })
      .withMessage('Mensagem deve ter entre 1 e 4000 caracteres'),
    body('delay')
      .optional()
      .isInt({ min: 1000, max: 60000 })
      .withMessage('Delay deve ser entre 1000ms e 60000ms')
  ],
  auditLog('SEND_BULK_WHATSAPP_MESSAGES'),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      const status = await whatsappClient?.getStatus();
      if (!whatsappClient || !status?.connected) {
        return res.status(503).json({
          error: 'WhatsApp não está conectado',
          code: 'WHATSAPP_NOT_CONNECTED'
        });
      }

      const { contacts, message, delay = 2000 } = req.body;

      const results = {
        total: contacts.length,
        sent: 0,
        failed: 0,
        details: [] as any[]
      };

      // Enviar mensagens com delay
      for (let i = 0; i < contacts.length; i++) {
        const contact = contacts[i];
        
        try {
          const success = await whatsappClient.sendMessage(contact.phone, message);
          
          if (success) {
            results.sent++;
            results.details.push({
              phone: contact.phone,
              status: 'sent',
              timestamp: new Date().toISOString()
            });
          } else {
            results.failed++;
            results.details.push({
              phone: contact.phone,
              status: 'failed',
              error: 'Falha no envio'
            });
          }
        } catch (error) {
          results.failed++;
          results.details.push({
            phone: contact.phone,
            status: 'failed',
            error: error instanceof Error ? error.message : 'Erro desconhecido'
          });
        }

        // Delay entre mensagens (exceto na última)
        if (i < contacts.length - 1) {
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }

      res.json({
        message: 'Envio em massa concluído',
        results,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Erro ao enviar mensagens em massa:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// POST /api/whatsapp/disconnect - Desconectar WhatsApp
router.post('/disconnect',
  authenticate,
  authorize('manage:system'),
  auditLog('DISCONNECT_WHATSAPP'),
  async (req: Request, res: Response) => {
    try {
      if (!whatsappClient) {
        return res.status(404).json({
          error: 'WhatsApp client não encontrado',
          code: 'CLIENT_NOT_FOUND'
        });
      }

      // O método disconnect não está implementado no cliente atual
      // Por enquanto, retornamos uma mensagem informativa
      res.json({
        message: 'Funcionalidade de desconexão não implementada no cliente atual',
        timestamp: new Date().toISOString(),
        note: 'Reinicie o servidor para desconectar o WhatsApp'
      });
    } catch (error) {
      console.error('Erro ao desconectar WhatsApp:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// GET /api/whatsapp/stats - Estatísticas do WhatsApp
router.get('/stats',
  authenticate,
  authorize('read:analytics'),
  async (req: Request, res: Response) => {
    try {
      // Estatísticas simplificadas usando contactService
      const totalContacts = await contactService.count({ isActive: true });
      const connectionStatus = whatsappClient ? await whatsappClient.getStatus() : null;

      const stats = {
        connection: {
          status: connectionStatus?.connected ? 'connected' : 'disconnected',
          clientInfo: connectionStatus?.info || null
        },
        messages: {
          total: 0,     // Não implementado no Supabase ainda
          today: 0,     // Não implementado no Supabase ainda
          sent: 0,      // Não implementado no Supabase ainda
          received: 0   // Não implementado no Supabase ainda
        },
        contacts: {
          unique: totalContacts
        },
        timestamp: new Date().toISOString()
      };

      res.json(stats);
    } catch (error) {
      console.error('Erro ao obter estatísticas WhatsApp:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

export default router;
