// Rotas atualizadas para usar apenas Supabase
import express from 'express';
import multer from 'multer';
import { contactService } from '../services/contactService';
import { messageService } from '../services/messageService';
import { supabase } from '../config/supabase';

// Configurar multer para armazenar arquivos na memória
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limite
  },
  fileFilter: (req, file, cb) => {
    // Aceitar apenas arquivos de áudio
    if (file.mimetype.startsWith('audio/')) {
      cb(null, true);
    } else {
      cb(null, false);
    }
  }
});

export function setupSupabaseRoutes(app: express.Application, geminiService?: any) {
  
  // =====================================================
  // ROTAS DE CONTATOS (SUPABASE)
  // =====================================================

  // Listar contatos
  app.get('/api/contacts', async (req, res) => {
    try {
      console.log('🔍 Requisição /api/contacts recebida - USANDO SUPABASE');
      
      const contacts = await contactService.findAll({
        isActive: true,
        limit: 100
      });

      console.log(`👥 Lista de gestantes solicitada - Encontradas: ${contacts.length}`);
      res.json(contacts);
    } catch (error: any) {
      console.error('❌ Erro ao listar contatos:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Criar contato
  app.post('/api/contacts', async (req, res) => {
    try {
      console.log('📝 Requisição POST /api/contacts recebida - USANDO SUPABASE');
      console.log('📝 Dados recebidos:', req.body);

      const { name, phone, babyGender } = req.body;

      // Validações básicas
      if (!name || !phone) {
        return res.status(400).json({
          error: 'Nome e telefone são obrigatórios',
          code: 'MISSING_REQUIRED_FIELDS'
        });
      }

      // Verificar se telefone já existe
      const existingContact = await contactService.findByPhone(phone);

      if (existingContact && existingContact.is_active) {
        return res.status(409).json({
          error: 'Telefone já cadastrado',
          code: 'PHONE_ALREADY_EXISTS'
        });
      }

      // Se existe mas está inativo, reativar
      if (existingContact && !existingContact.is_active) {
        console.log('📞 Reativando contato existente:', existingContact.id);

        const reactivatedContact = await contactService.update(existingContact.id!, {
          name,
          baby_gender: babyGender || 'unknown',
          is_active: true,
          last_interaction: new Date().toISOString()
        });

        console.log('✅ Contato reativado com sucesso:', existingContact.id);
        return res.status(201).json(reactivatedContact);
      }

      // Criar novo contato
      const newContact = await contactService.create({
        name,
        phone,
        baby_gender: babyGender || 'unknown',
        is_active: true,
        last_interaction: new Date().toISOString(),
        registration_status: 'unregistered',
        evaluation_messages: 0,
        interest_score: 0
      });

      console.log('✅ Gestante criada com sucesso:', newContact.id);
      res.status(201).json(newContact);
    } catch (error: any) {
      console.error('❌ Erro ao criar gestante:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR',
        details: error.message
      });
    }
  });

  // Atualizar contato
  app.put('/api/contacts/:id', async (req, res) => {
    try {
      console.log('📝 Requisição PUT /api/contacts recebida - USANDO SUPABASE');
      console.log('📝 ID:', req.params.id);
      console.log('📝 Dados recebidos:', req.body);

      const { name, phone, babyGender } = req.body;

      // Validações básicas
      if (!name || !phone) {
        return res.status(400).json({
          error: 'Nome e telefone são obrigatórios',
          code: 'MISSING_REQUIRED_FIELDS'
        });
      }

      const updatedContact = await contactService.update(req.params.id, {
        name,
        phone,
        baby_gender: babyGender || 'unknown',
        last_interaction: new Date().toISOString()
      });

      if (!updatedContact) {
        return res.status(404).json({
          error: 'Gestante não encontrada',
          code: 'NOT_FOUND'
        });
      }

      console.log('✅ Gestante atualizada com sucesso:', req.params.id);
      res.json(updatedContact);
    } catch (error: any) {
      console.error('❌ Erro ao atualizar gestante:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  });

  // Deletar contato (soft delete)
  app.delete('/api/contacts/:id', async (req, res) => {
    try {
      console.log('🗑️ Requisição DELETE /api/contacts recebida - USANDO SUPABASE');
      console.log('🗑️ ID:', req.params.id);

      const success = await contactService.delete(req.params.id);

      if (!success) {
        return res.status(404).json({
          error: 'Gestante não encontrada',
          code: 'NOT_FOUND'
        });
      }

      console.log('✅ Gestante deletada com sucesso (soft delete):', req.params.id);
      res.json({ message: 'Gestante deletada com sucesso' });
    } catch (error: any) {
      console.error('❌ Erro ao deletar gestante:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  });

  // =====================================================
  // ROTAS DE ANALYTICS (SUPABASE)
  // =====================================================

  // Analytics do dashboard
  app.get('/api/analytics/dashboard', async (req, res) => {
    try {
      console.log('📊 Analytics do dashboard solicitado - USANDO SUPABASE');

      // Buscar estatísticas dos contatos
      const totalContacts = await contactService.count({ isActive: true });

      // Estatísticas por gênero do bebê
      const genderStats = await contactService.countByBabyGender();

      // Estatísticas de mensagens
      const totalMessages = await messageService.count();
      const todayMessages = await messageService.countToday();

      const analytics = {
        totalContacts,
        babyGenders: {
          male: genderStats.male,
          female: genderStats.female,
          unknown: genderStats.unknown
        },
        messages: {
          total: totalMessages,
          today: todayMessages
        },
        lastUpdated: new Date().toISOString()
      };

      console.log(`📊 Analytics calculado - Gestantes encontradas: ${totalContacts}`);
      res.json(analytics);
    } catch (error: any) {
      console.error('❌ Erro ao calcular analytics:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Novas gestantes cadastradas (últimos 6 meses)
  app.get('/api/analytics/new-registrations', async (req, res) => {
    try {
      console.log('📊 Solicitação de novas gestantes cadastradas - USANDO SUPABASE');

      const chartData = await contactService.getRegistrationsByMonth();

      console.log(`📊 Novas gestantes por mês: ${chartData.length} meses com dados`);
      res.json(chartData);
    } catch (error: any) {
      console.error('❌ Erro ao obter dados de novas gestantes:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // =====================================================
  // ROTAS DE MENSAGENS (SUPABASE)
  // =====================================================

  // Obter mensagens de um contato
  app.get('/api/contacts/:id/messages', async (req, res) => {
    try {
      console.log('💬 Buscando mensagens do contato - USANDO SUPABASE');

      const messages = await messageService.findByContactId(req.params.id, 50);

      console.log(`💬 Encontradas ${messages.length} mensagens para o contato ${req.params.id}`);
      res.json(messages);
    } catch (error: any) {
      console.error('❌ Erro ao buscar mensagens:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Obter transcrições de áudios de um contato
  app.get('/api/contacts/:id/transcriptions', async (req, res) => {
    try {
      console.log('📝 Buscando transcrições de áudios do contato - USANDO SUPABASE');

      const { data: messages, error } = await supabase
        .from('messages')
        .select('id, content, transcription, timestamp, type')
        .eq('contact_id', req.params.id)
        .eq('type', 'audio')
        .not('transcription', 'is', null)
        .order('timestamp', { ascending: false });

      if (error) {
        console.error('❌ Erro ao buscar transcrições:', error);
        return res.status(500).json({ error: error.message });
      }

      console.log(`📝 Encontradas ${messages?.length || 0} transcrições para o contato ${req.params.id}`);
      res.json(messages || []);
    } catch (error: any) {
      console.error('❌ Erro ao buscar transcrições:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Listar todas as transcrições do sistema
  app.get('/api/transcriptions', async (req, res) => {
    try {
      console.log('📝 Listando todas as transcrições do sistema - USANDO SUPABASE');

      const limit = parseInt(req.query.limit as string) || 100;
      const offset = parseInt(req.query.offset as string) || 0;

      const { data: transcriptions, error } = await supabase
        .from('messages')
        .select(`
          id,
          content,
          transcription,
          timestamp,
          type,
          contacts!inner(id, name, phone)
        `)
        .eq('type', 'audio')
        .not('transcription', 'is', null)
        .order('timestamp', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        console.error('❌ Erro ao buscar transcrições:', error);
        return res.status(500).json({ error: error.message });
      }

      console.log(`📝 Encontradas ${transcriptions?.length || 0} transcrições no sistema`);
      res.json({
        success: true,
        data: transcriptions || [],
        pagination: {
          limit,
          offset,
          total: transcriptions?.length || 0
        }
      });
    } catch (error: any) {
      console.error('❌ Erro ao listar transcrições:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // =====================================================
  // ROTAS DE IA COM ÁUDIO (SUPABASE)
  // =====================================================

  // NOVO: Rota para receber um chat com áudio
  app.post('/api/chat/audio', upload.single('audio'), async (req, res) => {
    try {
      console.log('🎵 Requisição /api/chat/audio recebida - USANDO SUPABASE');

      // 1. Verifica se o arquivo de áudio foi realmente enviado
      if (!req.file) {
        return res.status(400).json({
          error: 'Arquivo de áudio não encontrado.',
          code: 'MISSING_AUDIO_FILE'
        });
      }

      console.log('🎵 Arquivo de áudio recebido:', {
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size
      });

      // 2. Obtém a mensagem de texto, se houver, do corpo da requisição
      const textMessage = req.body.message || "A usuária enviou uma mensagem de voz.";
      const contactId = req.body.contactId;

      if (!contactId) {
        return res.status(400).json({
          error: 'ID do contato é obrigatório',
          code: 'MISSING_CONTACT_ID'
        });
      }

      // 3. Busca o contato no banco de dados
      const contact = await contactService.findById(contactId);
      if (!contact) {
        return res.status(404).json({
          error: 'Contato não encontrado',
          code: 'CONTACT_NOT_FOUND'
        });
      }

      // 4. Verifica se o serviço Gemini está disponível
      if (!geminiService) {
        return res.status(503).json({
          error: 'Serviço de IA não disponível',
          code: 'AI_SERVICE_UNAVAILABLE'
        });
      }

      // 5. Chama o serviço Gemini, passando os dados do áudio
      console.log('🤖 Processando áudio com Gemini AI...');
      const geminiResponse = await geminiService.generateResponse(
        contact,
        textMessage,
        { buffer: req.file.buffer, mimetype: req.file.mimetype }
      );

      console.log('✅ Resposta da IA gerada com sucesso');

      // 6. Salva a mensagem no banco de dados (opcional)
      try {
        await messageService.create({
          contact_id: contactId,
          content: `[ÁUDIO] ${textMessage}`,
          type: 'audio',
          from_me: false,
          timestamp: new Date().toISOString(),
          sentiment: geminiResponse.sentiment?.type || 'neutral'
        });

        await messageService.create({
          contact_id: contactId,
          content: geminiResponse.response,
          type: 'text',
          from_me: true,
          timestamp: new Date().toISOString()
        });
      } catch (dbError) {
        console.warn('⚠️ Erro ao salvar mensagens no banco:', dbError);
        // Não falha a requisição por causa disso
      }

      // 7. Envia a resposta de TEXTO de volta para o cliente em formato JSON
      res.status(200).json({
        success: true,
        response: geminiResponse.response,
        sentiment: geminiResponse.sentiment,
        needs: geminiResponse.needs,
        suggestions: geminiResponse.suggestions,
        audioProcessed: true,
        timestamp: new Date().toISOString()
      });

    } catch (error: any) {
      console.error('❌ Erro no endpoint /api/chat/audio:', error);
      res.status(500).json({
        error: 'Erro interno ao processar o áudio.',
        code: 'AUDIO_PROCESSING_ERROR',
        details: error.message
      });
    }
  });

  // =====================================================
  // ROTAS DE UTILIDADES (SUPABASE)
  // =====================================================

  // Marcar contatos como registrados
  app.post('/api/contacts/mark-as-registered', async (req, res) => {
    try {
      console.log('📋 Marcando contatos como pré-cadastrados - USANDO SUPABASE');

      const { phones } = req.body;

      if (!phones || !Array.isArray(phones)) {
        return res.status(400).json({
          error: 'Lista de telefones é obrigatória',
          code: 'MISSING_PHONES'
        });
      }

      console.log(`📞 Telefones recebidos: ${phones.join(', ')}`);

      let updatedCount = 0;
      
      // Atualizar cada telefone individualmente
      for (const phone of phones) {
        const contact = await contactService.findByPhone(phone);
        if (contact) {
          await contactService.update(contact.id!, {
            registration_status: 'registered',
            is_active: true,
            last_interaction: new Date().toISOString()
          });
          updatedCount++;
        }
      }

      console.log(`✅ ${updatedCount} contatos marcados como 'registered'`);

      res.json({
        message: `${updatedCount} contatos marcados como gestantes pré-cadastradas`,
        updatedCount,
        phones
      });
    } catch (error: any) {
      console.error('❌ Erro ao marcar contatos como registrados:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  });

  // Rota para gerar sugestões da IA para conversas
  app.post('/api/ai/generate-suggestion', async (req, res) => {
    try {
      console.log('🤖 Gerando sugestão da IA para conversa...');

      const { prompt, systemInstruction, promptType, context } = req.body;

      if (!prompt) {
        return res.status(400).json({
          error: 'Prompt é obrigatório',
          code: 'MISSING_PROMPT'
        });
      }

      if (!geminiService) {
        return res.status(503).json({
          error: 'Serviço de IA não disponível',
          code: 'AI_SERVICE_UNAVAILABLE'
        });
      }

      console.log('🧠 Tipo de prompt:', promptType);
      console.log('📝 Contexto:', context?.substring(0, 100) + '...');

      // Gerar resposta usando o Gemini
      const fullPrompt = `${systemInstruction || "Você é um assistente virtual para suporte a gestantes. Seja empático, claro e forneça informações úteis e seguras. Responda em português brasileiro."}\n\n${prompt}`;

      const aiResponse = await geminiService.generateContent(fullPrompt);

      console.log('✅ Sugestão da IA gerada:', aiResponse.substring(0, 100) + '...');

      res.json({
        success: true,
        suggestion: aiResponse,
        promptType,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('❌ Erro ao gerar sugestão da IA:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  // =====================================================
  // ROTAS DE MENSAGENS (HISTÓRICO DE CONVERSAS)
  // =====================================================

  // Buscar mensagens de um contato específico
  app.get('/api/contacts/:id/messages', async (req, res) => {
    try {
      console.log('📱 Buscando mensagens do contato:', req.params.id);

      const contactId = req.params.id;
      const limit = parseInt(req.query.limit as string) || 50;
      const offset = parseInt(req.query.offset as string) || 0;

      // Verificar se o contato existe
      const { data: contact, error: contactError } = await supabase
        .from('contacts')
        .select('id, name')
        .eq('id', contactId)
        .single();

      if (contactError || !contact) {
        return res.status(404).json({
          error: 'Contato não encontrado',
          code: 'CONTACT_NOT_FOUND'
        });
      }

      // Buscar mensagens do contato
      const { data: messages, error: messagesError } = await supabase
        .from('messages')
        .select('*')
        .eq('contact_id', contactId)
        .order('timestamp', { ascending: true })
        .range(offset, offset + limit - 1);

      if (messagesError) {
        console.error('❌ Erro ao buscar mensagens:', messagesError);
        return res.status(500).json({
          error: 'Erro ao buscar mensagens',
          code: 'DATABASE_ERROR'
        });
      }

      console.log(`✅ ${messages?.length || 0} mensagens encontradas para ${contact.name}`);

      res.json({
        success: true,
        data: messages || [],
        contact: {
          id: contact.id,
          name: contact.name
        },
        pagination: {
          limit,
          offset,
          total: messages?.length || 0
        }
      });

    } catch (error) {
      console.error('❌ Erro ao buscar mensagens:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  });

  // Criar nova mensagem
  app.post('/api/contacts/:id/messages', async (req, res) => {
    try {
      console.log('📝 Criando nova mensagem para contato:', req.params.id);

      const contactId = req.params.id;
      const { content, type = 'text', fromMe = true } = req.body;

      if (!content) {
        return res.status(400).json({
          error: 'Conteúdo da mensagem é obrigatório',
          code: 'MISSING_CONTENT'
        });
      }

      // Verificar se o contato existe
      const { data: contact, error: contactError } = await supabase
        .from('contacts')
        .select('id, name')
        .eq('id', contactId)
        .single();

      if (contactError || !contact) {
        return res.status(404).json({
          error: 'Contato não encontrado',
          code: 'CONTACT_NOT_FOUND'
        });
      }

      // Criar mensagem
      const messageData = {
        contact_id: contactId,
        content,
        type,
        from_me: fromMe,
        timestamp: new Date().toISOString(),
        status: 'sent'
      };

      const { data: message, error: messageError } = await supabase
        .from('messages')
        .insert([messageData])
        .select()
        .single();

      if (messageError) {
        console.error('❌ Erro ao criar mensagem:', messageError);
        return res.status(500).json({
          error: 'Erro ao criar mensagem',
          code: 'DATABASE_ERROR'
        });
      }

      console.log(`✅ Mensagem criada para ${contact.name}:`, content.substring(0, 50) + '...');

      res.status(201).json({
        success: true,
        data: message,
        contact: {
          id: contact.id,
          name: contact.name
        }
      });

    } catch (error) {
      console.error('❌ Erro ao criar mensagem:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  });

  // Marcar mensagens como lidas
  app.patch('/api/contacts/:id/messages/read', async (req, res) => {
    try {
      console.log('👁️ Marcando mensagens como lidas para contato:', req.params.id);

      const contactId = req.params.id;

      const { data, error } = await supabase
        .from('messages')
        .update({ status: 'read' })
        .eq('contact_id', contactId)
        .eq('from_me', false)
        .neq('status', 'read');

      if (error) {
        console.error('❌ Erro ao marcar mensagens como lidas:', error);
        return res.status(500).json({
          error: 'Erro ao marcar mensagens como lidas',
          code: 'DATABASE_ERROR'
        });
      }

      console.log('✅ Mensagens marcadas como lidas');

      res.json({
        success: true,
        message: 'Mensagens marcadas como lidas'
      });

    } catch (error) {
      console.error('❌ Erro ao marcar mensagens como lidas:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  });

  console.log('✅ Rotas Supabase configuradas com sucesso');
}
