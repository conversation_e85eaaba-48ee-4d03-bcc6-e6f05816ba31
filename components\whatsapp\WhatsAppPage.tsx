
import React, { useState, useEffect, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';
import PageTitle from '../shared/PageTitle';
import WhatsAppConnectCard from './WhatsAppConnectCard';
import ConversationView from './ConversationView';
import { Contact, Message, WhatsAppConnectionStatus } from '../../src/types';
import { apiService } from '../../src/services/apiService';
import Select from './Select';
import Spinner from '../shared/Spinner';

const mapContactToContact = (contact: Contact): Contact => {
  return {
    ...contact,
    id: contact.id || contact._id || '',
    name: contact.name || '',
    phone: contact.phone || ''
  };
};


const fetchContactsNames = async (): Promise<{ id: string, name: string }[]> => {
  try {
    const contacts = await apiService.getContacts();
    return contacts.map(c => ({
      id: c._id || c.id || '',
      name: c.name || ''
    }));
  } catch (error) {
    console.error('Erro ao buscar contatos:', error);
    return [];
  }
};

const WhatsAppPage: React.FC = () => {
  const [connectionStatus, setConnectionStatus] = useState<WhatsAppConnectionStatus>(WhatsAppConnectionStatus.DISCONNECTED);
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);
  const [contactOptions, setContactOptions] = useState<{ value: string; label: string }[]>([]);
  const [allContacts, setAllContacts] = useState<Contact[]>([]);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [loadingContacts, setLoadingContacts] = useState(true);
  const [socket, setSocket] = useState<Socket | null>(null);

  // Socket.IO para receber QR Code em tempo real
  useEffect(() => {
    console.log('🔌 Conectando ao Socket.IO para QR Code...');

    const newSocket = io('http://localhost:3334');
    setSocket(newSocket);

    // Escutar evento de QR Code
    newSocket.on('qr', (qrCodeBase64: string) => {
      console.log('📱 QR Code recebido via Socket.IO');

      // Limpar prefixos duplicados se existirem
      let cleanBase64 = qrCodeBase64;
      if (cleanBase64.startsWith('data:image/png;base64,')) {
        cleanBase64 = cleanBase64.replace('data:image/png;base64,', '');
      }

      setQrCodeUrl(`data:image/png;base64,${cleanBase64}`);
      setConnectionStatus(WhatsAppConnectionStatus.QR_CODE);
    });

    // Escutar eventos de conexão
    newSocket.on('connect', () => {
      console.log('✅ Socket.IO conectado');
    });

    newSocket.on('disconnect', () => {
      console.log('❌ Socket.IO desconectado');
    });

    return () => {
      console.log('🔌 Desconectando Socket.IO...');
      newSocket.disconnect();
    };
  }, []);

  useEffect(() => {
    console.log('🔌 Verificando status do WhatsApp...');

    // Verificar status inicial do WhatsApp
    const checkWhatsAppStatus = async () => {
      try {
        const response = await fetch('http://localhost:3334/api/whatsapp-auto/status');
        const data = await response.json();
        console.log('📱 Status WhatsApp:', data);

        // Verificar se a estrutura da resposta está correta
        if (data.success && data.data && data.data.whatsapp && data.data.whatsapp.connected) {
          setConnectionStatus(WhatsAppConnectionStatus.CONNECTED);
          setQrCodeUrl(null); // Limpar QR Code quando conectado
        } else {
          setConnectionStatus(WhatsAppConnectionStatus.DISCONNECTED);
        }
      } catch (err) {
        console.error('❌ Erro ao verificar status WhatsApp:', err);
        setConnectionStatus(WhatsAppConnectionStatus.DISCONNECTED);
      }
    };

    checkWhatsAppStatus();

    const interval = setInterval(checkWhatsAppStatus, 30000);

    return () => {
      clearInterval(interval);
    };
  }, []);


  useEffect(() => {
    const loadContacts = async () => {
      setLoadingContacts(true);
      try {

        const contacts = await apiService.getContacts();
        const mappedContacts = contacts.map(mapContactToContact);
        setAllContacts(mappedContacts);


        const options = mappedContacts.map(c => ({
          value: c.id,
          label: c.name
        }));
        setContactOptions(options);

        console.log('✅ Contatos carregados para WhatsApp:', mappedContacts.length);
      } catch (error) {
        console.error('❌ Erro ao carregar contatos:', error);
      } finally {
        setLoadingContacts(false);
      }
    };
    loadContacts();
  }, []);

  // Conectar ao WhatsApp real
  const handleConnect = useCallback(async () => {
    console.log('🔌 Tentando conectar ao WhatsApp...');
    setConnectionStatus(WhatsAppConnectionStatus.CONNECTING);

    try {
      // Verificar status atual
      const statusResponse = await fetch('http://localhost:3334/api/whatsapp-auto/status');
      const statusData = await statusResponse.json();

      console.log('📱 Status WhatsApp atual:', statusData);

      // Verificar se a estrutura da resposta está correta
      if (statusData.success && statusData.data && statusData.data.whatsapp && statusData.data.whatsapp.connected) {
        setConnectionStatus(WhatsAppConnectionStatus.CONNECTED);
        setQrCodeUrl(null);
      } else {
        // Se não estiver conectado, aguardar QR Code via Socket.IO
        console.log('📱 Aguardando QR Code via Socket.IO...');
        setConnectionStatus(WhatsAppConnectionStatus.QR_CODE);

        // O QR Code será recebido automaticamente via Socket.IO
        // quando o backend gerar um novo
      }
    } catch (err) {
      console.error('❌ Erro ao conectar WhatsApp:', err);
      setConnectionStatus(WhatsAppConnectionStatus.ERROR);
    }
  }, []);

  const handleDisconnect = () => {
    console.log('🔌 Desconectando WhatsApp...');
    setConnectionStatus(WhatsAppConnectionStatus.DISCONNECTED);
    setQrCodeUrl(null);
  };
  
  const handleSelectContact = async (id: string) => {
    const contact = allContacts.find(c => c.id === id);
    if (contact) {
      setSelectedContact(contact);
      console.log('✅ Contato selecionado:', contact.name);

      // Carregar mensagens reais do histórico
      try {
        console.log('📱 Carregando histórico de mensagens para:', contact.name);
        const response = await fetch(`http://localhost:3334/api/contacts/${id}/messages`);

        if (response.ok) {
          const result = await response.json();
          console.log('📱 Resposta da API:', result);

          if (result.success && result.data) {
            const realMessages = result.data;
            console.log(`✅ ${realMessages.length} mensagens carregadas do histórico`);

            // Converter mensagens do backend para o formato do frontend
            const formattedMessages: Message[] = realMessages.map((msg: any) => ({
              id: msg.id,
              sender: msg.from_me ? 'user' : 'gestante',
              text: msg.content,
              timestamp: msg.timestamp,
              status: msg.status || 'delivered',
              // Incluir dados adicionais para áudio e transcrições
              type: msg.type,
              from_me: msg.from_me,
              content: msg.content
            }));

            setMessages(formattedMessages);

            // Marcar mensagens como lidas
            await fetch(`http://localhost:3334/api/contacts/${id}/messages/read`, {
              method: 'PATCH'
            });

          } else {
            console.log('📱 Nenhuma mensagem no histórico');
            setMessages([]);
          }
        } else {
          console.log('📱 Erro ao carregar mensagens, criando conversa nova');
          setMessages([]);
        }
      } catch (error) {
        console.error('❌ Erro ao carregar mensagens:', error);
        setMessages([]);
      }
    } else {
      setSelectedContact(null);
      setMessages([]);
      console.log('❌ Contato não encontrado:', id);
    }
  };

  const handleSendMessage = async (text: string): Promise<Message> => {
    if (!selectedContact) {
      throw new Error('Nenhum contato selecionado');
    }

    const newMessage: Message = {
      id: `msg_${Date.now()}`,
      sender: 'user',
      text,
      timestamp: new Date().toISOString(),
      status: 'sending',
    };

    // Adicionar mensagem localmente primeiro
    setMessages(prev => [...prev, newMessage]);

    try {
      // 1. Salvar mensagem no histórico primeiro
      const saveResponse = await fetch(`http://localhost:3334/api/contacts/${selectedContact.id}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: text,
          type: 'text',
          fromMe: true
        })
      });

      if (saveResponse.ok) {
        const savedMessage = await saveResponse.json();
        console.log('✅ Mensagem salva no histórico');

        // Atualizar a mensagem local com o ID real do banco
        setMessages(prev => prev.map(msg =>
          msg.id === newMessage.id ? { ...msg, id: savedMessage.data.id, status: 'saved' } : msg
        ));
      }

      // 2. Enviar mensagem via WhatsApp
      const whatsappResponse = await fetch('http://localhost:3334/api/whatsapp-auto/send-message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phoneNumber: selectedContact.phone,
          message: text
        })
      });

      if (whatsappResponse.ok) {
        console.log('✅ Mensagem enviada via WhatsApp');
        // Atualizar status da mensagem
        setMessages(prev => prev.map(msg =>
          msg.id === newMessage.id ? { ...msg, status: 'sent' } : msg
        ));
      } else {
        console.error('❌ Erro ao enviar via WhatsApp');
        setMessages(prev => prev.map(msg =>
          msg.id === newMessage.id ? { ...msg, status: 'failed' } : msg
        ));
      }
    } catch (error) {
      console.error('❌ Erro ao enviar mensagem:', error);
      setMessages(prev => prev.map(msg =>
        msg.id === newMessage.id ? { ...msg, status: 'failed' } : msg
      ));
    }

    return newMessage;
  };

  const handleSendAiMessage = async (text: string): Promise<Message> => {
    if (!selectedContact) {
      throw new Error('Nenhum contato selecionado');
    }

    const aiMessage: Message = {
      id: `msg_ai_${Date.now()}`,
      sender: 'ai',
      text,
      timestamp: new Date().toISOString(),
      status: 'sending',
    };

    // Adicionar mensagem localmente primeiro
    setMessages(prev => [...prev, aiMessage]);

    try {
      // 1. Salvar mensagem da IA no histórico primeiro
      const saveResponse = await fetch(`http://localhost:3334/api/contacts/${selectedContact.id}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: text,
          type: 'text',
          fromMe: true
        })
      });

      if (saveResponse.ok) {
        const savedMessage = await saveResponse.json();
        console.log('✅ Mensagem da IA salva no histórico');

        // Atualizar a mensagem local com o ID real do banco
        setMessages(prev => prev.map(msg =>
          msg.id === aiMessage.id ? { ...msg, id: savedMessage.data.id, status: 'saved' } : msg
        ));
      }

      // 2. Enviar mensagem via WhatsApp
      const whatsappResponse = await fetch('http://localhost:3334/api/whatsapp-auto/send-message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phoneNumber: selectedContact.phone,
          message: text
        })
      });

      if (whatsappResponse.ok) {
        console.log('✅ Mensagem da IA enviada via WhatsApp');
        setMessages(prev => prev.map(msg =>
          msg.id === aiMessage.id ? { ...msg, status: 'sent' } : msg
        ));
      } else {
        console.error('❌ Erro ao enviar mensagem da IA via WhatsApp');
        setMessages(prev => prev.map(msg =>
          msg.id === aiMessage.id ? { ...msg, status: 'failed' } : msg
        ));
      }
    } catch (error) {
      console.error('❌ Erro ao enviar mensagem da IA:', error);
      setMessages(prev => prev.map(msg =>
        msg.id === aiMessage.id ? { ...msg, status: 'failed' } : msg
      ));
    }

    return aiMessage;
  };


  return (
    <div>
      <PageTitle title="WhatsApp & Inteligência Artificial" subtitle="Conecte-se ao WhatsApp e utilize a IA para interações." />
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1">
          <WhatsAppConnectCard 
            status={connectionStatus}
            qrCodeUrl={qrCodeUrl}
            onConnect={handleConnect}
            onDisconnect={handleDisconnect}
          />
          <div className="mt-6 bg-white p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-neutral-dark mb-3">Selecionar Contato</h3>
            {loadingContacts ? <Spinner /> : (
              <Select
                options={contactOptions}
                value={selectedContact?.id || ''}
                onChange={(e) => handleSelectContact(e.target.value)}
                placeholder="Escolha um contato..."
              />
            )}
          </div>
        </div>

        <div className="lg:col-span-2">
          {selectedContact ? (
            <ConversationView
              contactName={selectedContact.name}
              messages={messages}
              onSendMessage={handleSendMessage}
              onSendAiMessage={handleSendAiMessage}
              currentContact={selectedContact}
            />
          ) : (
            <div className="bg-white p-6 rounded-lg shadow h-full flex items-center justify-center">
              <p className="text-gray-500">Selecione um contato para visualizar ou iniciar uma conversa.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default WhatsAppPage;
    